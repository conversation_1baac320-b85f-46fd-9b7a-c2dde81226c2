# Integration Webhooks API

A FastAPI application for processing webhooks and managing call data with MongoDB, replacing Google Sheets functionality.

## Features

- **Webhook Processing**: Receive and process webhook data from call systems
- **Call Log Management**: CRUD operations for call logs with filtering and pagination
- **Conversation Processing**: Process conversation data and create transcripts
- **Form Response Management**: Handle form responses with validation
- **Data Export**: Export data in JSON, CSV, and XLSX formats
- **MongoDB Integration**: Full MongoDB support with proper indexing
- **Async Processing**: Background task processing for webhooks
- **API Documentation**: Auto-generated OpenAPI/Swagger documentation

## Architecture

This application replaces the Google Sheets integration workflow with MongoDB operations:

- **Webhook Receiver** → Replaces `gateway:CustomWebHook`
- **Conversation Processing** → Replaces `BasicFeeder` + `TextAggregator` + `TransformToJSON`
- **Data Storage** → Replaces Google Sheets `filterRows` and `updateRow`
- **Export Functionality** → Replaces Google Sheets export features

## Quick Start

### Using Docker Compose (Recommended)

1. Clone the repository:
```bash
git clone <repository-url>
cd integration-webhooks
```

2. Copy environment file:
```bash
cp .env.example .env
```

3. Start the services:
```bash
docker-compose up -d
```

4. Access the application:
- API: http://localhost:8000
- API Documentation: http://localhost:8000/docs
- MongoDB Admin: http://localhost:8081 (admin/admin123)

### Manual Installation

1. Install Python 3.11+
2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Install and start MongoDB
4. Copy and configure environment:
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. Run the application:
```bash
uvicorn main:app --reload
```

## API Endpoints

### Webhooks
- `POST /api/v1/webhook/after-call` - Receive webhook data
- `GET /api/v1/webhook/status/{webhook_id}` - Get webhook status
- `POST /api/v1/webhook/retry/{webhook_id}` - Retry failed webhook

### Call Logs
- `GET /api/v1/call-logs` - List call logs (paginated)
- `GET /api/v1/call-logs/{unique_id}` - Get specific call log
- `POST /api/v1/call-logs` - Create call log
- `PUT /api/v1/call-logs/{unique_id}` - Update call log
- `DELETE /api/v1/call-logs/{unique_id}` - Delete call log
- `PATCH /api/v1/call-logs/{unique_id}/status` - Update status
- `GET /api/v1/call-logs/{unique_id}/summary` - Get summary
- `GET /api/v1/call-logs/stats/overview` - Get statistics

### Conversations
- `GET /api/v1/conversations` - List conversations (paginated)
- `GET /api/v1/conversations/{unique_id}` - Get specific conversation
- `POST /api/v1/conversations/process` - Process conversation data
- `GET /api/v1/conversations/{unique_id}/transcript` - Get transcript
- `POST /api/v1/conversations/{unique_id}/reprocess` - Reprocess conversation
- `DELETE /api/v1/conversations/{unique_id}` - Delete conversation
- `GET /api/v1/conversations/{unique_id}/analytics` - Get analytics
- `GET /api/v1/conversations/search/text` - Search by text

### Form Responses
- `GET /api/v1/form-responses` - List form responses (paginated)
- `GET /api/v1/form-responses/{unique_id}` - Get specific form response
- `POST /api/v1/form-responses` - Create form response
- `PUT /api/v1/form-responses/{unique_id}` - Update form response
- `DELETE /api/v1/form-responses/{unique_id}` - Delete form response
- `PATCH /api/v1/form-responses/{unique_id}/status` - Update status
- `GET /api/v1/form-responses/types/list` - Get form types
- `GET /api/v1/form-responses/stats/overview` - Get statistics
- `GET /api/v1/form-responses/{unique_id}/validation` - Validate response

### Export
- `GET /api/v1/export/call-logs` - Export call logs
- `GET /api/v1/export/form-responses` - Export form responses
- `GET /api/v1/export/conversations` - Export conversations
- `POST /api/v1/export/custom` - Custom export
- `GET /api/v1/export/templates` - Get export templates
- `GET /api/v1/export/status/{export_id}` - Get export status

## Configuration

Key environment variables:

```env
# Application
DEBUG=True
HOST=0.0.0.0
PORT=8000

# MongoDB
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=integration_webhooks

# Security
SECRET_KEY=your-secret-key
WEBHOOK_SECRET=webhook-secret-key

# CORS
ALLOWED_ORIGINS=["*"]
```

## Data Models

### Webhook Payload
```json
{
  "Unique_id": "call-123",
  "conversation": [
    {
      "timestamp": "2024-01-01T10:00:00Z",
      "speaker": "Agent",
      "text": "Hello, how can I help you?"
    }
  ],
  "call_start_time": "2024-01-01T10:00:00Z",
  "call_end_time": "2024-01-01T10:05:00Z",
  "call_duration": 300
}
```

### Call Log
```json
{
  "unique_id": "call-123",
  "call_start_time": "2024-01-01T10:00:00Z",
  "call_end_time": "2024-01-01T10:05:00Z",
  "call_duration_sec": 300,
  "call_duration_min": 5.0,
  "status": "completed",
  "notes": "Customer inquiry"
}
```

## Development

### Running Tests
```bash
pytest
```

### Code Formatting
```bash
black .
isort .
```

### Database Management
```bash
# Access MongoDB shell
docker-compose exec mongodb mongosh integration_webhooks

# View logs
docker-compose logs -f app
```

## Deployment

### Production Considerations

1. **Environment Variables**: Set proper production values
2. **Database**: Use MongoDB Atlas or dedicated MongoDB instance
3. **Security**: Configure proper CORS, authentication, and HTTPS
4. **Monitoring**: Add logging and monitoring solutions
5. **Scaling**: Consider using multiple workers and load balancing

### Docker Production
```bash
# Build production image
docker build -t integration-webhooks:prod .

# Run with production settings
docker run -d \
  -p 8000:8000 \
  -e DEBUG=False \
  -e MONGODB_URL=mongodb://your-mongo-host:27017 \
  integration-webhooks:prod
```

## Monitoring and Logging

The application includes:
- Structured logging with timestamps
- Health check endpoints
- Error tracking and webhook retry mechanisms
- Performance monitoring capabilities

## Support

For issues and questions:
1. Check the API documentation at `/docs`
2. Review the logs for error details
3. Ensure MongoDB is running and accessible
4. Verify environment configuration

## License

[Your License Here]
