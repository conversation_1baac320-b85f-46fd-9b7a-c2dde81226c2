# Simple Integration Webhooks API

A simple FastAPI application that processes webhooks and manages call data with MongoDB, replacing Google Sheets functionality.

## Quick Start

1. **Install dependencies:**
```bash
pip install -r requirements.txt
```

2. **Make sure MongoDB is running:**
```bash
# On macOS with Homebrew:
brew services start mongodb-community

# On Ubuntu:
sudo systemctl start mongod

# Or use Docker:
docker run -d -p 27017:27017 mongo:latest
```

3. **Run the application:**
```bash
python main.py
```

4. **Access the API:**
- API: http://localhost:8000
- Documentation: http://localhost:8000/docs

## API Endpoints

### Webhooks
- `POST /api/v1/webhook/after-call` - Receive webhook data
- `GET /api/v1/webhook/status/{webhook_id}` - Get webhook status

### Call Logs
- `GET /api/v1/call-logs` - List call logs
- `GET /api/v1/call-logs/{unique_id}` - Get specific call log
- `POST /api/v1/call-logs` - Create call log
- `PUT /api/v1/call-logs/{unique_id}` - Update call log
- `DELETE /api/v1/call-logs/{unique_id}` - Delete call log

### Conversations
- `GET /api/v1/conversations` - List conversations
- `GET /api/v1/conversations/{unique_id}` - Get specific conversation
- `GET /api/v1/conversations/{unique_id}/transcript` - Get transcript
- `DELETE /api/v1/conversations/{unique_id}` - Delete conversation

### Form Responses
- `GET /api/v1/form-responses` - List form responses
- `GET /api/v1/form-responses/{unique_id}` - Get specific form response
- `POST /api/v1/form-responses` - Create form response
- `PUT /api/v1/form-responses/{unique_id}` - Update form response
- `DELETE /api/v1/form-responses/{unique_id}` - Delete form response

### Export
- `GET /api/v1/export/call-logs?format=json|csv` - Export call logs
- `GET /api/v1/export/form-responses?format=json|csv` - Export form responses
- `GET /api/v1/export/conversations?format=json|csv` - Export conversations

## Example Webhook Payload

```json
{
  "Unique_id": "call-123",
  "conversation": [
    {
      "timestamp": "2024-01-01T10:00:00Z",
      "speaker": "Agent",
      "text": "Hello, how can I help you?"
    },
    {
      "timestamp": "2024-01-01T10:01:00Z",
      "speaker": "Customer",
      "text": "I need help with my account"
    }
  ],
  "call_start_time": "2024-01-01T10:00:00Z",
  "call_end_time": "2024-01-01T10:05:00Z",
  "call_duration": 300
}
```

## Configuration

The application uses these default settings:
- MongoDB URL: `mongodb://localhost:27017`
- Database Name: `integration_webhooks`
- Host: `0.0.0.0`
- Port: `8000`

To change these, edit the constants at the top of `main.py`.

## Features

- ✅ Webhook processing with background tasks
- ✅ Call log management with CRUD operations
- ✅ Conversation processing and transcript generation
- ✅ Form response handling
- ✅ Data export in JSON and CSV formats
- ✅ Automatic MongoDB indexing
- ✅ Pagination for list endpoints
- ✅ Auto-generated API documentation
