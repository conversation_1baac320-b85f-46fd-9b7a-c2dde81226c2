#!/bin/bash

# Integration Webhooks API Startup Script

echo "🚀 Starting Integration Webhooks API"
echo "=================================="

# Check if Docker is available
if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
    echo "📦 Docker detected. Starting with Docker Compose..."
    
    # Create .env file if it doesn't exist
    if [ ! -f .env ]; then
        echo "📝 Creating .env file from template..."
        cp .env.example .env
        echo "⚠️  Please review and update .env file with your configuration"
    fi
    
    # Start services
    docker-compose up -d
    
    echo "✅ Services started!"
    echo ""
    echo "🌐 API: http://localhost:8000"
    echo "📚 Documentation: http://localhost:8000/docs"
    echo "🗄️  MongoDB Admin: http://localhost:8081 (admin/admin123)"
    echo ""
    echo "📊 To view logs: docker-compose logs -f"
    echo "🛑 To stop: docker-compose down"
    
else
    echo "🐍 Docker not found. Starting with Python..."
    
    # Check if Python is available
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python 3 is required but not installed."
        exit 1
    fi
    
    # Check if MongoDB is running
    if ! pgrep -x "mongod" > /dev/null; then
        echo "⚠️  MongoDB is not running. Please start MongoDB first."
        echo "   On macOS: brew services start mongodb-community"
        echo "   On Ubuntu: sudo systemctl start mongod"
        exit 1
    fi
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        echo "📦 Creating virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Install dependencies
    echo "📥 Installing dependencies..."
    pip install -r requirements.txt
    
    # Create .env file if it doesn't exist
    if [ ! -f .env ]; then
        echo "📝 Creating .env file from template..."
        cp .env.example .env
        echo "⚠️  Please review and update .env file with your configuration"
    fi
    
    # Start the application
    echo "🚀 Starting FastAPI application..."
    uvicorn main:app --reload --host 0.0.0.0 --port 8000
fi
