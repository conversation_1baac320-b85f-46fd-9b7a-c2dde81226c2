"""
Simple FastAPI application for Integration Webhooks with MongoDB
Replaces Google Sheets functionality with MongoDB operations
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, BackgroundTasks, Query, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from enum import Enum
import uvicorn
import logging
import uuid
import json
import csv
import io
import math

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
MONGODB_URL = "mongodb://localhost:27017"
DATABASE_NAME = "integration_webhooks"

# Global database connection
client: Optional[AsyncIOMotorClient] = None
database: Optional[AsyncIOMotorDatabase] = None

# Collections
CALL_LOGS = "call_logs"
CONVERSATIONS = "conversations"
FORM_RESPONSES = "form_responses"
WEBHOOK_LOGS = "webhook_logs"


# Enums
class WebhookStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    RETRY = "retry"


# Models
class ConversationMessage(BaseModel):
    timestamp: datetime
    speaker: str
    text: str


class WebhookPayload(BaseModel):
    unique_id: str = Field(..., alias="Unique_id")
    conversation: List[ConversationMessage] = []
    call_start_time: Optional[datetime] = None
    call_end_time: Optional[datetime] = None
    call_duration: Optional[int] = None
    additional_data: Optional[Dict[str, Any]] = {}

    class Config:
        allow_population_by_field_name = True


class CallLogBase(BaseModel):
    unique_id: str
    call_start_time: Optional[datetime] = None
    call_end_time: Optional[datetime] = None
    call_duration_sec: Optional[int] = None
    call_duration_min: Optional[float] = None
    status: Optional[str] = None
    notes: Optional[str] = None


class CallLogCreate(CallLogBase):
    pass


class CallLogUpdate(BaseModel):
    call_start_time: Optional[datetime] = None
    call_end_time: Optional[datetime] = None
    call_duration_sec: Optional[int] = None
    call_duration_min: Optional[float] = None
    status: Optional[str] = None
    notes: Optional[str] = None


class CallLog(CallLogBase):
    id: str = Field(..., alias="_id")
    created_at: datetime
    updated_at: datetime

    class Config:
        allow_population_by_field_name = True


class ConversationBase(BaseModel):
    unique_id: str
    messages: List[ConversationMessage] = []
    full_transcript: Optional[str] = None
    formatted_transcript: Optional[str] = None


class Conversation(ConversationBase):
    id: str = Field(..., alias="_id")
    created_at: datetime
    updated_at: datetime

    class Config:
        allow_population_by_field_name = True


class FormResponseBase(BaseModel):
    unique_id: str
    response_data: Dict[str, Any] = {}
    form_type: Optional[str] = None
    status: Optional[str] = None


class FormResponseCreate(FormResponseBase):
    pass


class FormResponseUpdate(BaseModel):
    response_data: Optional[Dict[str, Any]] = None
    form_type: Optional[str] = None
    status: Optional[str] = None


class FormResponse(FormResponseBase):
    id: str = Field(..., alias="_id")
    created_at: datetime
    updated_at: datetime

    class Config:
        allow_population_by_field_name = True


class PaginatedResponse(BaseModel):
    items: List[Any]
    total: int
    page: int
    size: int
    pages: int


# Database functions
async def init_db():
    global client, database
    client = AsyncIOMotorClient(MONGODB_URL)
    database = client[DATABASE_NAME]

    # Create indexes
    await database[CALL_LOGS].create_index("unique_id", unique=True)
    await database[CALL_LOGS].create_index("call_start_time")
    await database[CONVERSATIONS].create_index("unique_id", unique=True)
    await database[FORM_RESPONSES].create_index("unique_id", unique=True)
    await database[WEBHOOK_LOGS].create_index("webhook_id")

    logger.info("Database initialized")


async def close_db():
    global client
    if client:
        client.close()


async def get_database():
    return database


# FastAPI app
app = FastAPI(
    title="Integration Webhooks API",
    description="FastAPI application for processing webhooks and managing call data with MongoDB",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.on_event("startup")
async def startup_event():
    await init_db()


@app.on_event("shutdown")
async def shutdown_event():
    await close_db()


@app.get("/")
async def root():
    return {
        "message": "Integration Webhooks API",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat()
    }


@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}


# Webhook Endpoints
@app.post("/api/v1/webhook/after-call")
async def receive_after_call_webhook(
    payload: Dict[str, Any],
    background_tasks: BackgroundTasks
):
    """Receive webhook data from call system"""
    webhook_id = str(uuid.uuid4())

    try:
        # Validate payload
        webhook_payload = WebhookPayload(**payload)

        # Log webhook
        db = await get_database()
        webhook_data = {
            "webhook_id": webhook_id,
            "unique_id": webhook_payload.unique_id,
            "status": WebhookStatus.PENDING.value,
            "payload": payload,
            "created_at": datetime.utcnow()
        }
        await db[WEBHOOK_LOGS].insert_one(webhook_data)

        # Process in background
        background_tasks.add_task(process_webhook_data, webhook_id, webhook_payload)

        return {
            "webhook_id": webhook_id,
            "status": "received",
            "unique_id": webhook_payload.unique_id
        }

    except Exception as e:
        logger.error(f"Webhook error: {e}")
        raise HTTPException(status_code=400, detail=str(e))


async def process_webhook_data(webhook_id: str, payload: WebhookPayload):
    """Background task to process webhook data"""
    try:
        db = await get_database()

        # Update webhook status
        await db[WEBHOOK_LOGS].update_one(
            {"webhook_id": webhook_id},
            {"$set": {"status": WebhookStatus.PROCESSING.value}}
        )

        # Process conversation
        if payload.conversation:
            formatted_lines = []
            for msg in payload.conversation:
                timestamp_str = msg.timestamp.strftime("%Y-%m-%d %H:%M:%S")
                line = f"{timestamp_str} - {msg.speaker}: {msg.text}"
                formatted_lines.append(line)

            formatted_transcript = "\n".join(formatted_lines)
            full_transcript = json.dumps(formatted_transcript)

            conversation_data = {
                "unique_id": payload.unique_id,
                "messages": [msg.dict() for msg in payload.conversation],
                "formatted_transcript": formatted_transcript,
                "full_transcript": full_transcript,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }

            await db[CONVERSATIONS].replace_one(
                {"unique_id": payload.unique_id},
                conversation_data,
                upsert=True
            )

        # Update/create call log
        call_log_data = {
            "unique_id": payload.unique_id,
            "call_start_time": payload.call_start_time,
            "call_end_time": payload.call_end_time,
            "call_duration_sec": payload.call_duration,
            "updated_at": datetime.utcnow()
        }

        if payload.call_duration:
            call_log_data["call_duration_min"] = round(payload.call_duration / 60, 2)

        existing = await db[CALL_LOGS].find_one({"unique_id": payload.unique_id})
        if not existing:
            call_log_data["created_at"] = datetime.utcnow()

        await db[CALL_LOGS].replace_one(
            {"unique_id": payload.unique_id},
            call_log_data,
            upsert=True
        )

        # Mark webhook as successful
        await db[WEBHOOK_LOGS].update_one(
            {"webhook_id": webhook_id},
            {"$set": {
                "status": WebhookStatus.SUCCESS.value,
                "processed_at": datetime.utcnow()
            }}
        )

    except Exception as e:
        logger.error(f"Background processing error: {e}")
        await db[WEBHOOK_LOGS].update_one(
            {"webhook_id": webhook_id},
            {"$set": {
                "status": WebhookStatus.FAILED.value,
                "error_message": str(e)
            }}
        )


@app.get("/api/v1/webhook/status/{webhook_id}")
async def get_webhook_status(webhook_id: str):
    """Get webhook processing status"""
    db = await get_database()
    webhook_log = await db[WEBHOOK_LOGS].find_one({"webhook_id": webhook_id})

    if not webhook_log:
        raise HTTPException(status_code=404, detail="Webhook not found")

    return {
        "webhook_id": webhook_id,
        "status": webhook_log["status"],
        "unique_id": webhook_log["unique_id"],
        "created_at": webhook_log["created_at"],
        "processed_at": webhook_log.get("processed_at"),
        "error_message": webhook_log.get("error_message")
    }


# Call Logs Endpoints
@app.get("/api/v1/call-logs", response_model=PaginatedResponse)
async def get_call_logs(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    search: Optional[str] = Query(None)
):
    """Get paginated list of call logs"""
    db = await get_database()

    query = {}
    if status:
        query["status"] = status
    if search:
        query["$or"] = [
            {"unique_id": {"$regex": search, "$options": "i"}},
            {"notes": {"$regex": search, "$options": "i"}}
        ]

    total = await db[CALL_LOGS].count_documents(query)
    skip = (page - 1) * size
    pages = math.ceil(total / size)

    cursor = db[CALL_LOGS].find(query).sort("created_at", -1).skip(skip).limit(size)
    call_logs = []

    async for call_log_data in cursor:
        call_log_data["_id"] = str(call_log_data["_id"])
        call_logs.append(CallLog(**call_log_data))

    return PaginatedResponse(
        items=call_logs,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@app.get("/api/v1/call-logs/{unique_id}", response_model=CallLog)
async def get_call_log(unique_id: str):
    """Get call log by unique ID"""
    db = await get_database()
    call_log_data = await db[CALL_LOGS].find_one({"unique_id": unique_id})

    if not call_log_data:
        raise HTTPException(status_code=404, detail="Call log not found")

    call_log_data["_id"] = str(call_log_data["_id"])
    return CallLog(**call_log_data)


@app.post("/api/v1/call-logs", response_model=CallLog)
async def create_call_log(call_log_data: CallLogCreate):
    """Create new call log"""
    db = await get_database()

    # Check if exists
    existing = await db[CALL_LOGS].find_one({"unique_id": call_log_data.unique_id})
    if existing:
        raise HTTPException(status_code=400, detail="Call log already exists")

    call_log_dict = call_log_data.dict()
    call_log_dict["created_at"] = datetime.utcnow()
    call_log_dict["updated_at"] = datetime.utcnow()

    # Calculate duration
    if call_log_dict.get("call_start_time") and call_log_dict.get("call_end_time"):
        duration_sec = (call_log_dict["call_end_time"] - call_log_dict["call_start_time"]).total_seconds()
        call_log_dict["call_duration_sec"] = int(duration_sec)
        call_log_dict["call_duration_min"] = round(duration_sec / 60, 2)

    result = await db[CALL_LOGS].insert_one(call_log_dict)
    call_log_dict["_id"] = str(result.inserted_id)

    return CallLog(**call_log_dict)


@app.put("/api/v1/call-logs/{unique_id}", response_model=CallLog)
async def update_call_log(unique_id: str, update_data: CallLogUpdate):
    """Update call log"""
    db = await get_database()

    update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
    if not update_dict:
        raise HTTPException(status_code=400, detail="No data to update")

    update_dict["updated_at"] = datetime.utcnow()

    result = await db[CALL_LOGS].update_one(
        {"unique_id": unique_id},
        {"$set": update_dict}
    )

    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Call log not found")

    # Return updated call log
    call_log_data = await db[CALL_LOGS].find_one({"unique_id": unique_id})
    call_log_data["_id"] = str(call_log_data["_id"])
    return CallLog(**call_log_data)


@app.delete("/api/v1/call-logs/{unique_id}")
async def delete_call_log(unique_id: str):
    """Delete call log"""
    db = await get_database()
    result = await db[CALL_LOGS].delete_one({"unique_id": unique_id})

    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Call log not found")

    return {"message": f"Call log {unique_id} deleted successfully"}


# Conversations Endpoints
@app.get("/api/v1/conversations", response_model=PaginatedResponse)
async def get_conversations(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None)
):
    """Get paginated list of conversations"""
    db = await get_database()

    query = {}
    if search:
        query["$or"] = [
            {"unique_id": {"$regex": search, "$options": "i"}},
            {"formatted_transcript": {"$regex": search, "$options": "i"}}
        ]

    total = await db[CONVERSATIONS].count_documents(query)
    skip = (page - 1) * size
    pages = math.ceil(total / size)

    cursor = db[CONVERSATIONS].find(query).sort("created_at", -1).skip(skip).limit(size)
    conversations = []

    async for conv_data in cursor:
        conv_data["_id"] = str(conv_data["_id"])
        if "messages" in conv_data:
            conv_data["messages"] = [ConversationMessage(**msg) for msg in conv_data["messages"]]
        conversations.append(Conversation(**conv_data))

    return PaginatedResponse(
        items=conversations,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@app.get("/api/v1/conversations/{unique_id}", response_model=Conversation)
async def get_conversation(unique_id: str):
    """Get conversation by unique ID"""
    db = await get_database()
    conv_data = await db[CONVERSATIONS].find_one({"unique_id": unique_id})

    if not conv_data:
        raise HTTPException(status_code=404, detail="Conversation not found")

    conv_data["_id"] = str(conv_data["_id"])
    if "messages" in conv_data:
        conv_data["messages"] = [ConversationMessage(**msg) for msg in conv_data["messages"]]

    return Conversation(**conv_data)


@app.get("/api/v1/conversations/{unique_id}/transcript")
async def get_conversation_transcript(
    unique_id: str,
    format: str = Query("formatted", regex="^(formatted|raw|json)$")
):
    """Get conversation transcript in different formats"""
    db = await get_database()
    conv_data = await db[CONVERSATIONS].find_one({"unique_id": unique_id})

    if not conv_data:
        raise HTTPException(status_code=404, detail="Conversation not found")

    if format == "formatted":
        return {
            "unique_id": unique_id,
            "transcript": conv_data.get("formatted_transcript", ""),
            "format": "formatted"
        }
    elif format == "raw":
        return {
            "unique_id": unique_id,
            "messages": conv_data.get("messages", []),
            "format": "raw"
        }
    elif format == "json":
        return {
            "unique_id": unique_id,
            "transcript": conv_data.get("full_transcript", ""),
            "format": "json"
        }


@app.delete("/api/v1/conversations/{unique_id}")
async def delete_conversation(unique_id: str):
    """Delete conversation"""
    db = await get_database()
    result = await db[CONVERSATIONS].delete_one({"unique_id": unique_id})

    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Conversation not found")

    return {"message": f"Conversation {unique_id} deleted successfully"}


# Form Responses Endpoints
@app.get("/api/v1/form-responses", response_model=PaginatedResponse)
async def get_form_responses(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    form_type: Optional[str] = Query(None),
    status: Optional[str] = Query(None)
):
    """Get paginated list of form responses"""
    db = await get_database()

    query = {}
    if form_type:
        query["form_type"] = form_type
    if status:
        query["status"] = status

    total = await db[FORM_RESPONSES].count_documents(query)
    skip = (page - 1) * size
    pages = math.ceil(total / size)

    cursor = db[FORM_RESPONSES].find(query).sort("created_at", -1).skip(skip).limit(size)
    form_responses = []

    async for form_data in cursor:
        form_data["_id"] = str(form_data["_id"])
        form_responses.append(FormResponse(**form_data))

    return PaginatedResponse(
        items=form_responses,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@app.get("/api/v1/form-responses/{unique_id}", response_model=FormResponse)
async def get_form_response(unique_id: str):
    """Get form response by unique ID"""
    db = await get_database()
    form_data = await db[FORM_RESPONSES].find_one({"unique_id": unique_id})

    if not form_data:
        raise HTTPException(status_code=404, detail="Form response not found")

    form_data["_id"] = str(form_data["_id"])
    return FormResponse(**form_data)


@app.post("/api/v1/form-responses", response_model=FormResponse)
async def create_form_response(form_response_data: FormResponseCreate):
    """Create new form response"""
    db = await get_database()

    # Check if exists
    existing = await db[FORM_RESPONSES].find_one({"unique_id": form_response_data.unique_id})
    if existing:
        raise HTTPException(status_code=400, detail="Form response already exists")

    form_dict = form_response_data.dict()
    form_dict["created_at"] = datetime.utcnow()
    form_dict["updated_at"] = datetime.utcnow()

    result = await db[FORM_RESPONSES].insert_one(form_dict)
    form_dict["_id"] = str(result.inserted_id)

    return FormResponse(**form_dict)


@app.put("/api/v1/form-responses/{unique_id}", response_model=FormResponse)
async def update_form_response(unique_id: str, update_data: FormResponseUpdate):
    """Update form response"""
    db = await get_database()

    update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
    if not update_dict:
        raise HTTPException(status_code=400, detail="No data to update")

    update_dict["updated_at"] = datetime.utcnow()

    result = await db[FORM_RESPONSES].update_one(
        {"unique_id": unique_id},
        {"$set": update_dict}
    )

    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Form response not found")

    # Return updated form response
    form_data = await db[FORM_RESPONSES].find_one({"unique_id": unique_id})
    form_data["_id"] = str(form_data["_id"])
    return FormResponse(**form_data)


@app.delete("/api/v1/form-responses/{unique_id}")
async def delete_form_response(unique_id: str):
    """Delete form response"""
    db = await get_database()
    result = await db[FORM_RESPONSES].delete_one({"unique_id": unique_id})

    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Form response not found")

    return {"message": f"Form response {unique_id} deleted successfully"}


# Export Endpoints
@app.get("/api/v1/export/call-logs")
async def export_call_logs(format: str = Query("json", regex="^(json|csv)$")):
    """Export call logs data"""
    db = await get_database()

    cursor = db[CALL_LOGS].find({}).sort("created_at", -1)
    call_logs = []

    async for call_log_data in cursor:
        call_log_data["_id"] = str(call_log_data["_id"])
        call_logs.append(call_log_data)

    if format == "json":
        return Response(
            content=json.dumps(call_logs, indent=2, default=str),
            media_type="application/json",
            headers={"Content-Disposition": "attachment; filename=call_logs.json"}
        )
    elif format == "csv":
        if not call_logs:
            return Response(content="", media_type="text/csv")

        output = io.StringIO()
        fieldnames = call_logs[0].keys()
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(call_logs)

        return Response(
            content=output.getvalue(),
            media_type="text/csv",
            headers={"Content-Disposition": "attachment; filename=call_logs.csv"}
        )


@app.get("/api/v1/export/form-responses")
async def export_form_responses(format: str = Query("json", regex="^(json|csv)$")):
    """Export form responses data"""
    db = await get_database()

    cursor = db[FORM_RESPONSES].find({}).sort("created_at", -1)
    form_responses = []

    async for form_data in cursor:
        form_data["_id"] = str(form_data["_id"])
        # Flatten response_data for CSV export
        if format == "csv" and "response_data" in form_data:
            response_data = form_data.pop("response_data", {})
            for key, value in response_data.items():
                form_data[f"response_{key}"] = value
        form_responses.append(form_data)

    if format == "json":
        return Response(
            content=json.dumps(form_responses, indent=2, default=str),
            media_type="application/json",
            headers={"Content-Disposition": "attachment; filename=form_responses.json"}
        )
    elif format == "csv":
        if not form_responses:
            return Response(content="", media_type="text/csv")

        output = io.StringIO()
        fieldnames = form_responses[0].keys()
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(form_responses)

        return Response(
            content=output.getvalue(),
            media_type="text/csv",
            headers={"Content-Disposition": "attachment; filename=form_responses.csv"}
        )


@app.get("/api/v1/export/conversations")
async def export_conversations(
    format: str = Query("json", regex="^(json|csv)$"),
    include_transcripts: bool = Query(False)
):
    """Export conversations data"""
    db = await get_database()

    cursor = db[CONVERSATIONS].find({}).sort("created_at", -1)
    conversations = []

    async for conv_data in cursor:
        conv_data["_id"] = str(conv_data["_id"])
        if not include_transcripts:
            conv_data.pop("formatted_transcript", None)
            conv_data.pop("full_transcript", None)
        if format == "csv":
            conv_data["message_count"] = len(conv_data.get("messages", []))
            conv_data.pop("messages", None)  # Remove complex nested data for CSV
        conversations.append(conv_data)

    if format == "json":
        return Response(
            content=json.dumps(conversations, indent=2, default=str),
            media_type="application/json",
            headers={"Content-Disposition": "attachment; filename=conversations.json"}
        )
    elif format == "csv":
        if not conversations:
            return Response(content="", media_type="text/csv")

        output = io.StringIO()
        fieldnames = conversations[0].keys()
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(conversations)

        return Response(
            content=output.getvalue(),
            media_type="text/csv",
            headers={"Content-Disposition": "attachment; filename=conversations.csv"}
        )


# Run the application
if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
