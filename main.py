"""
FastAPI application for Integration Webhooks with MongoDB
Replaces Google Sheets functionality with MongoDB operations
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn
from datetime import datetime
import logging

from app.database import init_db, close_db
from app.routers import webhooks, call_logs, conversations, form_responses, export
from app.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting up Integration Webhooks API...")
    await init_db()
    yield
    # Shutdown
    logger.info("Shutting down Integration Webhooks API...")
    await close_db()


# Create FastAPI app
app = FastAPI(
    title="Integration Webhooks API",
    description="FastAPI application for processing webhooks and managing call data with MongoDB",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(webhooks.router, prefix="/api/v1", tags=["webhooks"])
app.include_router(call_logs.router, prefix="/api/v1", tags=["call-logs"])
app.include_router(conversations.router, prefix="/api/v1", tags=["conversations"])
app.include_router(form_responses.router, prefix="/api/v1", tags=["form-responses"])
app.include_router(export.router, prefix="/api/v1", tags=["export"])


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Integration Webhooks API",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat(),
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat()
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
