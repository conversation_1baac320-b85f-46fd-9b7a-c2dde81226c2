// MongoDB initialization script
db = db.getSiblingDB('integration_webhooks');

// Create collections
db.createCollection('call_logs');
db.createCollection('conversations');
db.createCollection('form_responses');
db.createCollection('webhook_logs');

// Create indexes for better performance
db.call_logs.createIndex({ "unique_id": 1 }, { unique: true });
db.call_logs.createIndex({ "call_start_time": 1 });
db.call_logs.createIndex({ "created_at": 1 });
db.call_logs.createIndex({ "status": 1 });

db.conversations.createIndex({ "unique_id": 1 }, { unique: true });
db.conversations.createIndex({ "created_at": 1 });
db.conversations.createIndex({ "formatted_transcript": "text" });

db.form_responses.createIndex({ "unique_id": 1 }, { unique: true });
db.form_responses.createIndex({ "created_at": 1 });
db.form_responses.createIndex({ "form_type": 1 });
db.form_responses.createIndex({ "status": 1 });

db.webhook_logs.createIndex({ "webhook_id": 1 });
db.webhook_logs.createIndex({ "unique_id": 1 });
db.webhook_logs.createIndex({ "created_at": 1 });
db.webhook_logs.createIndex({ "status": 1 });

print('Database initialized successfully');
