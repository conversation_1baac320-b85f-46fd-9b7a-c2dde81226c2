"""
Test configuration and fixtures
"""

import pytest
import asyncio
from httpx import AsyncClient
from motor.motor_asyncio import AsyncIOMotorClient
import os

# Set test environment
os.environ["MONGODB_URL"] = "mongodb://localhost:27017"
os.environ["DATABASE_NAME"] = "integration_webhooks_test"
os.environ["DEBUG"] = "True"

from main import app
from app.database import init_db, close_db


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def setup_database():
    """Setup test database"""
    await init_db()
    yield
    await close_db()


@pytest.fixture
async def client(setup_database):
    """Create test client"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
async def clean_database():
    """Clean database before each test"""
    from app.database import get_database
    from app.config import Collections
    
    db = await get_database()
    
    # Clean all collections
    await db[Collections.CALL_LOGS].delete_many({})
    await db[Collections.CONVERSATIONS].delete_many({})
    await db[Collections.FORM_RESPONSES].delete_many({})
    await db[Collections.WEBHOOK_LOGS].delete_many({})
    
    yield
    
    # Clean again after test
    await db[Collections.CALL_LOGS].delete_many({})
    await db[Collections.CONVERSATIONS].delete_many({})
    await db[Collections.FORM_RESPONSES].delete_many({})
    await db[Collections.WEBHOOK_LOGS].delete_many({})


@pytest.fixture
def sample_call_log():
    """Sample call log data"""
    return {
        "unique_id": "test-call-001",
        "call_start_time": "2024-01-01T10:00:00Z",
        "call_end_time": "2024-01-01T10:05:00Z",
        "call_duration_sec": 300,
        "call_duration_min": 5.0,
        "status": "completed",
        "notes": "Test call log"
    }


@pytest.fixture
def sample_conversation():
    """Sample conversation data"""
    return {
        "unique_id": "test-conversation-001",
        "messages": [
            {
                "timestamp": "2024-01-01T10:00:00Z",
                "speaker": "Agent",
                "text": "Hello, how can I help you?"
            },
            {
                "timestamp": "2024-01-01T10:01:00Z",
                "speaker": "Customer",
                "text": "I need help with my account"
            }
        ]
    }


@pytest.fixture
def sample_form_response():
    """Sample form response data"""
    return {
        "unique_id": "test-form-001",
        "form_type": "contact",
        "response_data": {
            "name": "John Doe",
            "email": "<EMAIL>",
            "phone": "+**********",
            "message": "Test message"
        },
        "status": "submitted"
    }
