"""
Test cases for webhook endpoints
"""

import pytest
from httpx import AsyncClient
from datetime import datetime


@pytest.mark.asyncio
async def test_webhook_after_call(client: AsyncClient):
    """Test webhook after call endpoint"""
    payload = {
        "Unique_id": "test-call-123",
        "conversation": [
            {
                "timestamp": "2024-01-01T10:00:00Z",
                "speaker": "Agent",
                "text": "Hello, how can I help you?"
            },
            {
                "timestamp": "2024-01-01T10:01:00Z",
                "speaker": "Customer",
                "text": "I need help with my account"
            }
        ],
        "call_start_time": "2024-01-01T10:00:00Z",
        "call_end_time": "2024-01-01T10:05:00Z",
        "call_duration": 300,
        "additional_data": {
            "agent_id": "agent-001",
            "customer_id": "customer-456"
        }
    }
    
    response = await client.post("/api/v1/webhook/after-call", json=payload)
    
    assert response.status_code == 200
    data = response.json()
    assert "webhook_id" in data
    assert data["status"] == "received"
    assert data["unique_id"] == "test-call-123"


@pytest.mark.asyncio
async def test_webhook_status(client: AsyncClient):
    """Test webhook status endpoint"""
    # First create a webhook
    payload = {
        "Unique_id": "test-call-status",
        "conversation": [],
        "call_start_time": "2024-01-01T10:00:00Z"
    }
    
    webhook_response = await client.post("/api/v1/webhook/after-call", json=payload)
    webhook_data = webhook_response.json()
    webhook_id = webhook_data["webhook_id"]
    
    # Check status
    status_response = await client.get(f"/api/v1/webhook/status/{webhook_id}")
    
    assert status_response.status_code == 200
    status_data = status_response.json()
    assert status_data["webhook_id"] == webhook_id
    assert status_data["unique_id"] == "test-call-status"
    assert "status" in status_data


@pytest.mark.asyncio
async def test_webhook_invalid_payload(client: AsyncClient):
    """Test webhook with invalid payload"""
    invalid_payload = {
        "invalid_field": "test"
    }
    
    response = await client.post("/api/v1/webhook/after-call", json=invalid_payload)
    
    assert response.status_code == 400
    assert "Invalid webhook payload" in response.json()["detail"]


@pytest.mark.asyncio
async def test_webhook_nonexistent_status(client: AsyncClient):
    """Test getting status for non-existent webhook"""
    response = await client.get("/api/v1/webhook/status/nonexistent-webhook-id")
    
    assert response.status_code == 404
    assert "Webhook not found" in response.json()["detail"]
