# Application Configuration
APP_NAME=Integration Webhooks API
VERSION=1.0.0
DEBUG=True
HOST=0.0.0.0
PORT=8000

# MongoDB Configuration
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=integration_webhooks

# Security Configuration
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Webhook Configuration
WEBHOOK_SECRET=webhook-secret-key
MAX_WEBHOOK_RETRIES=3

# Logging Configuration
LOG_LEVEL=INFO

# Pagination Configuration
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100
