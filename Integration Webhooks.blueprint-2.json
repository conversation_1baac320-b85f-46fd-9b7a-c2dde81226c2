{"name": "Integration Webhooks", "flow": [{"id": 1, "module": "gateway:CustomWebHook", "version": 1, "parameters": {"hook": 2173882, "maxResults": 1}, "mapper": {}, "metadata": {"designer": {"x": -3125, "y": -13}, "restore": {"parameters": {"hook": {"data": {"editable": "true"}, "label": "After Call Receiver"}}}, "parameters": [{"name": "hook", "type": "hook:gateway-webhook", "label": "Webhook", "required": true}, {"name": "maxResults", "type": "number", "label": "Maximum number of results"}]}}, {"id": 4, "module": "builtin:BasicFeeder", "version": 1, "parameters": {}, "mapper": {"array": "{{1.conversation}}"}, "metadata": {"designer": {"x": -2793, "y": 9}, "restore": {"expect": {"array": {"mode": "edit"}}}, "expect": [{"mode": "edit", "name": "array", "spec": [], "type": "array", "label": "Array"}]}}, {"id": 9, "module": "util:TextAggregator", "version": 1, "parameters": {"feeder": 4, "rowSeparator": "\n"}, "mapper": {"value": "{{formatDate(4.timestamp; \"YYYY-MM-DD HH:mm:ss\")}} - {{4.speaker}}: {{4.text}}"}, "metadata": {"designer": {"x": -2492, "y": 18}, "restore": {"extra": {"feeder": {"label": "Iterator [4]"}}, "parameters": {"rowSeparator": {"label": "New row"}}}, "parameters": [{"name": "rowSeparator", "type": "select", "label": "Row separator", "validate": {"enum": ["\n", "\t", "other"]}}], "expect": [{"name": "value", "type": "text", "label": "Text"}], "advanced": true}}, {"id": 51, "module": "json:TransformToJSON", "version": 1, "parameters": {"space": ""}, "mapper": {"object": "{{9.text}}"}, "metadata": {"designer": {"x": -2032, "y": 22, "name": "JSON string for full transcript"}, "restore": {"parameters": {"space": {"label": "Empty"}}}, "parameters": [{"name": "space", "type": "select", "label": "Indentation", "validate": {"enum": ["tab", "2", "4"]}}], "expect": [{"name": "object", "type": "any", "label": "Object"}]}}, {"id": 48, "module": "google-sheets:filterRows", "version": 2, "parameters": {"__IMTCONN__": 7621135}, "mapper": {"from": "drive", "limit": "1", "filter": [[{"a": "A", "b": "{{1.Unique_id}}", "o": "text:equal"}]], "sheetId": "Call Log Details", "sortOrder": "asc", "spreadsheetId": "1I1zy3OaEj2qEZTTJVE8raTi6c_nfvNDhf6OnFvJ5h_Y", "tableFirstRow": "A1:CZ1", "includesHeaders": true, "valueRenderOption": "FORMATTED_VALUE", "dateTimeRenderOption": "FORMATTED_STRING"}, "metadata": {"designer": {"x": -1661, "y": 16, "name": "Log Call Details"}, "restore": {"expect": {"from": {"label": "Select from My Drive"}, "orderBy": {"mode": "chose"}, "sheetId": {"mode": "chose", "label": "Call Log Details"}, "sortOrder": {"mode": "chose", "label": "Ascending"}, "spreadsheetId": {"mode": "chose", "label": "<PERSON><PERSON>a <PERSON>p Client Data"}, "tableFirstRow": {"label": "A-CZ"}, "includesHeaders": {"mode": "chose", "label": "Yes"}, "valueRenderOption": {"mode": "chose", "label": "Formatted value"}, "dateTimeRenderOption": {"mode": "chose", "label": "Formatted string"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "from", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["drive", "share"]}}, {"name": "valueRenderOption", "type": "select", "label": "Value render option", "validate": {"enum": ["FORMATTED_VALUE", "UNFORMATTED_VALUE", "FORMULA"]}}, {"name": "dateTimeRenderOption", "type": "select", "label": "Date and time render option", "validate": {"enum": ["SERIAL_NUMBER", "FORMATTED_STRING"]}}, {"name": "limit", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Limit"}, {"name": "spreadsheetId", "type": "select", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "tableFirstRow", "type": "select", "label": "Column range", "required": true, "validate": {"enum": ["A1:Z1", "A1:BZ1", "A1:CZ1", "A1:DZ1", "A1:MZ1", "A1:ZZ1", "A1:AZZ1", "A1:BZZ1", "A1:CZZ1", "A1:DZZ1", "A1:MZZ1", "A1:ZZZ1"]}}, {"name": "filter", "type": "filter", "label": "Filter", "options": "rpc://google-sheets/2/rpcGetFilterKeys?includesHeaders=true"}, {"name": "orderBy", "type": "select", "label": "Order by"}, {"name": "sortOrder", "type": "select", "label": "Sort order", "validate": {"enum": ["asc", "desc"]}}], "interface": [{"name": "__IMTLENGTH__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total number of bundles"}, {"name": "__IMTINDEX__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Bundle order position"}, {"name": "__ROW_NUMBER__", "type": "number", "label": "Row number"}, {"name": "__SPREADSHEET_ID__", "type": "text", "label": "Spreadsheet ID"}, {"name": "__SHEET__", "type": "text", "label": "Sheet"}, {"name": "0", "type": "text", "label": "Unique Id (A)"}, {"name": "1", "type": "text", "label": "Call Start Time (B)"}, {"name": "2", "type": "text", "label": "Call End Time (C)"}, {"name": "3", "type": "text", "label": "Call Duration (Sec) (D)"}, {"name": "4", "type": "text", "label": "Call Duration (Min) (E)"}, {"name": "5", "type": "text", "label": "(F)"}, {"name": "6", "type": "text", "label": "(G)"}, {"name": "7", "type": "text", "label": "(H)"}, {"name": "8", "type": "text", "label": "(I)"}, {"name": "9", "type": "text", "label": "(J)"}, {"name": "10", "type": "text", "label": "(K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}, {"name": "26", "type": "text", "label": "(AA)"}, {"name": "27", "type": "text", "label": "(AB)"}, {"name": "28", "type": "text", "label": "(AC)"}, {"name": "29", "type": "text", "label": "(AD)"}, {"name": "30", "type": "text", "label": "(AE)"}, {"name": "31", "type": "text", "label": "(AF)"}, {"name": "32", "type": "text", "label": "(AG)"}, {"name": "33", "type": "text", "label": "(AH)"}, {"name": "34", "type": "text", "label": "(AI)"}, {"name": "35", "type": "text", "label": "(AJ)"}, {"name": "36", "type": "text", "label": "(AK)"}, {"name": "37", "type": "text", "label": "(AL)"}, {"name": "38", "type": "text", "label": "(AM)"}, {"name": "39", "type": "text", "label": "(AN)"}, {"name": "40", "type": "text", "label": "(AO)"}, {"name": "41", "type": "text", "label": "(AP)"}, {"name": "42", "type": "text", "label": "(AQ)"}, {"name": "43", "type": "text", "label": "(AR)"}, {"name": "44", "type": "text", "label": "(AS)"}, {"name": "45", "type": "text", "label": "(AT)"}, {"name": "46", "type": "text", "label": "(AU)"}, {"name": "47", "type": "text", "label": "(AV)"}, {"name": "48", "type": "text", "label": "(AW)"}, {"name": "49", "type": "text", "label": "(AX)"}, {"name": "50", "type": "text", "label": "(AY)"}, {"name": "51", "type": "text", "label": "(AZ)"}, {"name": "52", "type": "text", "label": "(BA)"}, {"name": "53", "type": "text", "label": "(BB)"}, {"name": "54", "type": "text", "label": "(BC)"}, {"name": "55", "type": "text", "label": "(BD)"}, {"name": "56", "type": "text", "label": "(BE)"}, {"name": "57", "type": "text", "label": "(BF)"}, {"name": "58", "type": "text", "label": "(BG)"}, {"name": "59", "type": "text", "label": "(BH)"}, {"name": "60", "type": "text", "label": "(BI)"}, {"name": "61", "type": "text", "label": "(BJ)"}, {"name": "62", "type": "text", "label": "(BK)"}, {"name": "63", "type": "text", "label": "(BL)"}, {"name": "64", "type": "text", "label": "(BM)"}, {"name": "65", "type": "text", "label": "(BN)"}, {"name": "66", "type": "text", "label": "(BO)"}, {"name": "67", "type": "text", "label": "(BP)"}, {"name": "68", "type": "text", "label": "(BQ)"}, {"name": "69", "type": "text", "label": "(BR)"}, {"name": "70", "type": "text", "label": "(BS)"}, {"name": "71", "type": "text", "label": "(BT)"}, {"name": "72", "type": "text", "label": "(BU)"}, {"name": "73", "type": "text", "label": "(BV)"}, {"name": "74", "type": "text", "label": "(BW)"}, {"name": "75", "type": "text", "label": "(BX)"}, {"name": "76", "type": "text", "label": "(BY)"}, {"name": "77", "type": "text", "label": "(BZ)"}, {"name": "78", "type": "text", "label": "(CA)"}, {"name": "79", "type": "text", "label": "(CB)"}, {"name": "80", "type": "text", "label": "(CC)"}, {"name": "81", "type": "text", "label": "(CD)"}, {"name": "82", "type": "text", "label": "(CE)"}, {"name": "83", "type": "text", "label": "(CF)"}, {"name": "84", "type": "text", "label": "(CG)"}, {"name": "85", "type": "text", "label": "(CH)"}, {"name": "86", "type": "text", "label": "(CI)"}, {"name": "87", "type": "text", "label": "(CJ)"}, {"name": "88", "type": "text", "label": "(CK)"}, {"name": "89", "type": "text", "label": "(CL)"}, {"name": "90", "type": "text", "label": "(CM)"}, {"name": "91", "type": "text", "label": "(CN)"}, {"name": "92", "type": "text", "label": "(CO)"}, {"name": "93", "type": "text", "label": "(CP)"}, {"name": "94", "type": "text", "label": "(CQ)"}, {"name": "95", "type": "text", "label": "(CR)"}, {"name": "96", "type": "text", "label": "(CS)"}, {"name": "97", "type": "text", "label": "(CT)"}, {"name": "98", "type": "text", "label": "(CU)"}, {"name": "99", "type": "text", "label": "(CV)"}, {"name": "100", "type": "text", "label": "(CW)"}, {"name": "101", "type": "text", "label": "(CX)"}, {"name": "102", "type": "text", "label": "(CY)"}, {"name": "103", "type": "text", "label": "(CZ)"}]}}, {"id": 49, "module": "google-sheets:updateRow", "version": 2, "parameters": {"__IMTCONN__": 7621135}, "mapper": {"from": "drive", "mode": "select", "values": {"2": "{{formatDate(parseDate(now; \"YYYY-MM-DDTHH:mm:ss.SSSZ\"); \"M/D/YYYY HH:mm:ss\")}}"}, "sheetId": "Call Log Details", "rowNumber": "{{48.`__ROW_NUMBER__`}}", "spreadsheetId": "/1I1zy3OaEj2qEZTTJVE8raTi6c_nfvNDhf6OnFvJ5h_Y", "includesHeaders": true, "valueInputOption": "USER_ENTERED"}, "metadata": {"designer": {"x": -1384, "y": 19}, "restore": {"expect": {"from": {"label": "My Drive"}, "mode": {"label": "Search by path"}, "sheetId": {"label": "Call Log Details"}, "spreadsheetId": {"path": ["<PERSON><PERSON>a <PERSON>p Client Data"]}, "includesHeaders": {"label": "Yes", "nested": [{"name": "values", "spec": [{"name": "0", "type": "text", "label": "Unique Id (A)"}, {"name": "1", "type": "text", "label": "Call Start Time (B)"}, {"name": "2", "type": "text", "label": "Call End Time (C)"}, {"name": "3", "type": "text", "label": "Call Duration (D)"}, {"name": "4", "type": "text", "label": "(E)"}, {"name": "5", "type": "text", "label": "(F)"}, {"name": "6", "type": "text", "label": "(G)"}, {"name": "7", "type": "text", "label": "(H)"}, {"name": "8", "type": "text", "label": "(I)"}, {"name": "9", "type": "text", "label": "(J)"}, {"name": "10", "type": "text", "label": "(K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}]}, "valueInputOption": {"mode": "chose", "label": "User entered"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "mode", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["select", "fromAll", "map"]}}, {"name": "valueInputOption", "type": "select", "label": "Value input option", "validate": {"enum": ["USER_ENTERED", "RAW"]}}, {"name": "from", "type": "select", "label": "Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "spreadsheetId", "type": "file", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "rowNumber", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Row number", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "values", "spec": [{"name": "0", "type": "text", "label": "Unique Id (A)"}, {"name": "1", "type": "text", "label": "Call Start Time (B)"}, {"name": "2", "type": "text", "label": "Call End Time (C)"}, {"name": "3", "type": "text", "label": "Call Duration (D)"}, {"name": "4", "type": "text", "label": "(E)"}, {"name": "5", "type": "text", "label": "(F)"}, {"name": "6", "type": "text", "label": "(G)"}, {"name": "7", "type": "text", "label": "(H)"}, {"name": "8", "type": "text", "label": "(I)"}, {"name": "9", "type": "text", "label": "(J)"}, {"name": "10", "type": "text", "label": "(K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}]}}, {"id": 20, "module": "google-sheets:filterRows", "version": 2, "parameters": {"__IMTCONN__": 7621135}, "mapper": {"from": "drive", "filter": [[{"a": "F", "b": "{{1.Unique_id}}", "o": "text:equal"}]], "sheetId": "Updated Format of Form Responses", "sortOrder": "asc", "spreadsheetId": "1I1zy3OaEj2qEZTTJVE8raTi6c_nfvNDhf6OnFvJ5h_Y", "tableFirstRow": "A1:CZ1", "includesHeaders": true, "valueRenderOption": "FORMATTED_VALUE", "dateTimeRenderOption": "FORMATTED_STRING"}, "metadata": {"designer": {"x": -1079, "y": 34}, "restore": {"expect": {"from": {"label": "Select from My Drive"}, "orderBy": {"mode": "chose"}, "sheetId": {"mode": "chose", "label": "Updated Format of Form Responses"}, "sortOrder": {"mode": "chose", "label": "Ascending"}, "spreadsheetId": {"mode": "chose", "label": "<PERSON><PERSON>a <PERSON>p Client Data"}, "tableFirstRow": {"label": "A-CZ"}, "includesHeaders": {"mode": "chose", "label": "Yes"}, "valueRenderOption": {"mode": "chose", "label": "Formatted value"}, "dateTimeRenderOption": {"mode": "chose", "label": "Formatted string"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "from", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["drive", "share"]}}, {"name": "valueRenderOption", "type": "select", "label": "Value render option", "validate": {"enum": ["FORMATTED_VALUE", "UNFORMATTED_VALUE", "FORMULA"]}}, {"name": "dateTimeRenderOption", "type": "select", "label": "Date and time render option", "validate": {"enum": ["SERIAL_NUMBER", "FORMATTED_STRING"]}}, {"name": "limit", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Limit"}, {"name": "spreadsheetId", "type": "select", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "tableFirstRow", "type": "select", "label": "Column range", "required": true, "validate": {"enum": ["A1:Z1", "A1:BZ1", "A1:CZ1", "A1:DZ1", "A1:MZ1", "A1:ZZ1", "A1:AZZ1", "A1:BZZ1", "A1:CZZ1", "A1:DZZ1", "A1:MZZ1", "A1:ZZZ1"]}}, {"name": "filter", "type": "filter", "label": "Filter", "options": "rpc://google-sheets/2/rpcGetFilterKeys?includesHeaders=true"}, {"name": "orderBy", "type": "select", "label": "Order by"}, {"name": "sortOrder", "type": "select", "label": "Sort order", "validate": {"enum": ["asc", "desc"]}}], "interface": [{"name": "__IMTLENGTH__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total number of bundles"}, {"name": "__IMTINDEX__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Bundle order position"}, {"name": "__ROW_NUMBER__", "type": "number", "label": "Row number"}, {"name": "__SPREADSHEET_ID__", "type": "text", "label": "Spreadsheet ID"}, {"name": "__SHEET__", "type": "text", "label": "Sheet"}, {"name": "0", "type": "text", "label": "Timestamp (A)"}, {"name": "1", "type": "text", "label": "First Name (B)"}, {"name": "2", "type": "text", "label": "Last Name (C)"}, {"name": "3", "type": "text", "label": "Contact No. (D)"}, {"name": "4", "type": "text", "label": "Project (E)"}, {"name": "5", "type": "text", "label": "Unique Id (F)"}, {"name": "6", "type": "text", "label": "Client Call Connection Status (G)"}, {"name": "7", "type": "text", "label": "Full Call Conversation (H)"}, {"name": "8", "type": "text", "label": "Call Summary (I)"}, {"name": "9", "type": "text", "label": "Lead Type (Hot/Cold) (J)"}, {"name": "10", "type": "text", "label": "Site Visit Booked (K)"}, {"name": "11", "type": "text", "label": "Visit Booking Date and Time (L)"}, {"name": "12", "type": "text", "label": "Does this user needs Immediate call from the Sales Team (M)"}, {"name": "13", "type": "text", "label": "Reason for Yes/No for attention from the Sales Team (N)"}, {"name": "14", "type": "text", "label": "Was there a glitch in the final Call (O)"}, {"name": "15", "type": "text", "label": "Number of Times Call Tried (Agent to Client) (P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}, {"name": "26", "type": "text", "label": "(AA)"}, {"name": "27", "type": "text", "label": "(AB)"}, {"name": "28", "type": "text", "label": "(AC)"}, {"name": "29", "type": "text", "label": "(AD)"}, {"name": "30", "type": "text", "label": "(AE)"}, {"name": "31", "type": "text", "label": "(AF)"}, {"name": "32", "type": "text", "label": "(AG)"}, {"name": "33", "type": "text", "label": "(AH)"}, {"name": "34", "type": "text", "label": "(AI)"}, {"name": "35", "type": "text", "label": "(AJ)"}, {"name": "36", "type": "text", "label": "(AK)"}, {"name": "37", "type": "text", "label": "(AL)"}, {"name": "38", "type": "text", "label": "(AM)"}, {"name": "39", "type": "text", "label": "(AN)"}, {"name": "40", "type": "text", "label": "(AO)"}, {"name": "41", "type": "text", "label": "(AP)"}, {"name": "42", "type": "text", "label": "(AQ)"}, {"name": "43", "type": "text", "label": "(AR)"}, {"name": "44", "type": "text", "label": "(AS)"}, {"name": "45", "type": "text", "label": "(AT)"}, {"name": "46", "type": "text", "label": "(AU)"}, {"name": "47", "type": "text", "label": "(AV)"}, {"name": "48", "type": "text", "label": "(AW)"}, {"name": "49", "type": "text", "label": "(AX)"}, {"name": "50", "type": "text", "label": "(AY)"}, {"name": "51", "type": "text", "label": "(AZ)"}, {"name": "52", "type": "text", "label": "(BA)"}, {"name": "53", "type": "text", "label": "(BB)"}, {"name": "54", "type": "text", "label": "(BC)"}, {"name": "55", "type": "text", "label": "(BD)"}, {"name": "56", "type": "text", "label": "(BE)"}, {"name": "57", "type": "text", "label": "(BF)"}, {"name": "58", "type": "text", "label": "(BG)"}, {"name": "59", "type": "text", "label": "(BH)"}, {"name": "60", "type": "text", "label": "(BI)"}, {"name": "61", "type": "text", "label": "(BJ)"}, {"name": "62", "type": "text", "label": "(BK)"}, {"name": "63", "type": "text", "label": "(BL)"}, {"name": "64", "type": "text", "label": "(BM)"}, {"name": "65", "type": "text", "label": "(BN)"}, {"name": "66", "type": "text", "label": "(BO)"}, {"name": "67", "type": "text", "label": "(BP)"}, {"name": "68", "type": "text", "label": "(BQ)"}, {"name": "69", "type": "text", "label": "(BR)"}, {"name": "70", "type": "text", "label": "(BS)"}, {"name": "71", "type": "text", "label": "(BT)"}, {"name": "72", "type": "text", "label": "(BU)"}, {"name": "73", "type": "text", "label": "(BV)"}, {"name": "74", "type": "text", "label": "(BW)"}, {"name": "75", "type": "text", "label": "(BX)"}, {"name": "76", "type": "text", "label": "(BY)"}, {"name": "77", "type": "text", "label": "(BZ)"}, {"name": "78", "type": "text", "label": "(CA)"}, {"name": "79", "type": "text", "label": "(CB)"}, {"name": "80", "type": "text", "label": "(CC)"}, {"name": "81", "type": "text", "label": "(CD)"}, {"name": "82", "type": "text", "label": "(CE)"}, {"name": "83", "type": "text", "label": "(CF)"}, {"name": "84", "type": "text", "label": "(CG)"}, {"name": "85", "type": "text", "label": "(CH)"}, {"name": "86", "type": "text", "label": "(CI)"}, {"name": "87", "type": "text", "label": "(CJ)"}, {"name": "88", "type": "text", "label": "(CK)"}, {"name": "89", "type": "text", "label": "(CL)"}, {"name": "90", "type": "text", "label": "(CM)"}, {"name": "91", "type": "text", "label": "(CN)"}, {"name": "92", "type": "text", "label": "(CO)"}, {"name": "93", "type": "text", "label": "(CP)"}, {"name": "94", "type": "text", "label": "(CQ)"}, {"name": "95", "type": "text", "label": "(CR)"}, {"name": "96", "type": "text", "label": "(CS)"}, {"name": "97", "type": "text", "label": "(CT)"}, {"name": "98", "type": "text", "label": "(CU)"}, {"name": "99", "type": "text", "label": "(CV)"}, {"name": "100", "type": "text", "label": "(CW)"}, {"name": "101", "type": "text", "label": "(CX)"}, {"name": "102", "type": "text", "label": "(CY)"}, {"name": "103", "type": "text", "label": "(CZ)"}]}}, {"id": 34, "module": "util:<PERSON>witcher", "version": 1, "parameters": {}, "mapper": {"input": "{{20.`15`}}", "casesTable": [{"output": "twice", "pattern": "once"}, {"output": "thrice", "pattern": "twice"}, {"output": "thrice", "pattern": "thrice"}], "elseOutput": "once", "useRegExpMatch": true}, "metadata": {"designer": {"x": -794, "y": 47}, "restore": {"expect": {"casesTable": {"mode": "chose", "items": [null, null, null]}}}, "expect": [{"name": "input", "type": "text", "label": "Input"}, {"name": "useRegExpMatch", "type": "boolean", "label": "Use regular expressions to match", "required": true}, {"name": "casesTable", "spec": [{"name": "pattern", "type": "text", "label": "Pattern"}, {"name": "output", "type": "any", "label": "Output"}], "type": "array", "label": "Cases", "required": true}, {"name": "elseOutput", "type": "any", "label": "Else"}]}}, {"id": 37, "module": "builtin:BasicRouter", "version": 1, "mapper": null, "metadata": {"designer": {"x": -518, "y": 45}}, "routes": [{"flow": [{"id": 31, "module": "google-sheets:updateRow", "version": 2, "parameters": {"__IMTCONN__": 7621135}, "filter": {"name": "If Call not Connected", "conditions": [[{"a": "{{1.dial_status}}", "b": "completed", "o": "text:notequal"}, {"a": "{{20.`6`}}", "b": "completed", "o": "text:notequal"}]]}, "mapper": {"from": "drive", "mode": "select", "values": {"6": "{{1.dial_status}}", "7": "-", "8": "-", "9": "-", "10": "-", "11": "-", "12": "-", "13": "-", "14": "-", "15": "{{34.output}}"}, "sheetId": "Updated Format of Form Responses", "rowNumber": "{{20.`__ROW_NUMBER__`}}", "spreadsheetId": "/1I1zy3OaEj2qEZTTJVE8raTi6c_nfvNDhf6OnFvJ5h_Y", "includesHeaders": true, "valueInputOption": "USER_ENTERED"}, "metadata": {"designer": {"x": -536, "y": 778}, "restore": {"expect": {"from": {"label": "My Drive"}, "mode": {"label": "Search by path"}, "sheetId": {"label": "Updated Format of Form Responses"}, "spreadsheetId": {"path": ["<PERSON><PERSON>a <PERSON>p Client Data"]}, "includesHeaders": {"label": "Yes", "nested": [{"name": "values", "spec": [{"name": "0", "type": "text", "label": "Timestamp (A)"}, {"name": "1", "type": "text", "label": "First Name (B)"}, {"name": "2", "type": "text", "label": "Last Name (C)"}, {"name": "3", "type": "text", "label": "Contact No. (D)"}, {"name": "4", "type": "text", "label": "Project (E)"}, {"name": "5", "type": "text", "label": "Unique Id (F)"}, {"name": "6", "type": "text", "label": "Client Call Connection Status (G)"}, {"name": "7", "type": "text", "label": "Full Call Conversation (H)"}, {"name": "8", "type": "text", "label": "Call Summary (I)"}, {"name": "9", "type": "text", "label": "Lead Type (Hot/Cold) (J)"}, {"name": "10", "type": "text", "label": "Site Visit Booked (K)"}, {"name": "11", "type": "text", "label": "Visit Booking Date and Time (L)"}, {"name": "12", "type": "text", "label": "Does this user needs Immediate call from the Sales Team (M)"}, {"name": "13", "type": "text", "label": "Reason for Yes/No for attention from the Sales Team (N)"}, {"name": "14", "type": "text", "label": "Was there a glitch in the final Call (O)"}, {"name": "15", "type": "text", "label": "Number of Times Call Tried (Agent to Client) (P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}], "type": "collection", "label": "Values"}]}, "valueInputOption": {"mode": "chose", "label": "User entered"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "mode", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["select", "fromAll", "map"]}}, {"name": "valueInputOption", "type": "select", "label": "Value input option", "validate": {"enum": ["USER_ENTERED", "RAW"]}}, {"name": "from", "type": "select", "label": "Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "spreadsheetId", "type": "file", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "rowNumber", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Row number", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "values", "spec": [{"name": "0", "type": "text", "label": "Timestamp (A)"}, {"name": "1", "type": "text", "label": "First Name (B)"}, {"name": "2", "type": "text", "label": "Last Name (C)"}, {"name": "3", "type": "text", "label": "Contact No. (D)"}, {"name": "4", "type": "text", "label": "Project (E)"}, {"name": "5", "type": "text", "label": "Unique Id (F)"}, {"name": "6", "type": "text", "label": "Client Call Connection Status (G)"}, {"name": "7", "type": "text", "label": "Full Call Conversation (H)"}, {"name": "8", "type": "text", "label": "Call Summary (I)"}, {"name": "9", "type": "text", "label": "Lead Type (Hot/Cold) (J)"}, {"name": "10", "type": "text", "label": "Site Visit Booked (K)"}, {"name": "11", "type": "text", "label": "Visit Booking Date and Time (L)"}, {"name": "12", "type": "text", "label": "Does this user needs Immediate call from the Sales Team (M)"}, {"name": "13", "type": "text", "label": "Reason for Yes/No for attention from the Sales Team (N)"}, {"name": "14", "type": "text", "label": "Was there a glitch in the final Call (O)"}, {"name": "15", "type": "text", "label": "Number of Times Call Tried (Agent to Client) (P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}], "type": "collection", "label": "Values"}]}}, {"id": 56, "module": "http:ActionSendData", "version": 3, "parameters": {"handleErrors": true, "useNewZLibDeCompress": true}, "mapper": {"ca": "", "qs": [], "url": "https://client1.phlexibit.com/api/create-lead", "data": "{\n    \"first_name\": \"{{20.`1`}}\",\n    \"last_name\": \"{{20.`2`}}\",\n    \"google_sheet_id\": \"{{20.`5`}}\",\n    \"contact_number\": \"+{{20.`3`}}\",\n    \"requirement\": \"NA\",\n    \"call_status\": \"{{1.dial_status}}\",\n    \"projectName\": \"Naroda Lavish\",\n    \"full_conversation\": \"NA\",\n    \"call_summary\": \"NA\",\n    \"is_hot_lead\":false,\n    \"reached\":false,\n    \"transfered\":false,\n    \"site_visit\": \"\",\n\"reason_of_transfer\": \"NA\"\n}", "gzip": true, "method": "post", "headers": [{"name": "Content-Type", "value": "application/json"}], "timeout": "", "useMtls": false, "authPass": "", "authUser": "", "bodyType": "raw", "contentType": "application/json", "serializeUrl": false, "shareCookies": false, "parseResponse": true, "followRedirect": true, "useQuerystring": false, "followAllRedirects": false, "rejectUnauthorized": true}, "metadata": {"designer": {"x": -162, "y": 766}, "restore": {"expect": {"qs": {"mode": "chose"}, "method": {"mode": "chose", "label": "POST"}, "headers": {"mode": "chose", "items": [null]}, "bodyType": {"label": "Raw"}, "contentType": {"label": "JSON (application/json)"}}}, "parameters": [{"name": "handleErrors", "type": "boolean", "label": "Evaluate all states as errors (except for 2xx and 3xx )", "required": true}, {"name": "useNewZLibDeCompress", "type": "hidden"}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "serializeUrl", "type": "boolean", "label": "Serialize URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["get", "head", "post", "put", "patch", "delete", "options"]}}, {"name": "headers", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Headers"}, {"name": "qs", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Query String"}, {"name": "bodyType", "type": "select", "label": "Body type", "validate": {"enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]}}, {"name": "parseResponse", "type": "boolean", "label": "Parse response", "required": true}, {"name": "authUser", "type": "text", "label": "User name"}, {"name": "authPass", "type": "password", "label": "Password"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout", "validate": {"max": 300, "min": 1}}, {"name": "shareCookies", "type": "boolean", "label": "Share cookies with other HTTP modules", "required": true}, {"name": "ca", "type": "cert", "label": "Self-signed certificate"}, {"name": "rejectUnauthorized", "type": "boolean", "label": "Reject connections that are using unverified (self-signed) certificates", "required": true}, {"name": "followRedirect", "type": "boolean", "label": "Follow redirect", "required": true}, {"name": "useQuerystring", "type": "boolean", "label": "Disable serialization of multiple same query string keys as arrays", "required": true}, {"name": "gzip", "type": "boolean", "label": "Request compressed content", "required": true}, {"name": "useMtls", "type": "boolean", "label": "Use Mutual TLS", "required": true}, {"name": "contentType", "type": "select", "label": "Content type", "validate": {"enum": ["text/plain", "application/json", "application/xml", "text/xml", "text/html", "custom"]}}, {"name": "data", "type": "buffer", "label": "Request content"}, {"name": "followAllRedirects", "type": "boolean", "label": "Follow all redirect", "required": true}]}}]}, {"flow": [{"id": 38, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 7621612}, "filter": {"name": "If Call Connected", "conditions": [[{"a": "{{1.dial_status}}", "b": "completed", "o": "text:equal"}]]}, "mapper": {"model": "gpt-4o-mini", "top_p": "1", "select": "chat", "messages": [{"role": "user", "content": "This is the conversation dialouge between AI Voice Calling Agent from SatyaSankalp Developers and a client: {{9.text}}\n\nPlease determine, wether there was any glitch in the call?\n\nGlitch here means that the user got disconnected abruptly or the conversation looks short or incomplete. This can be due to network issues. Your job is to identify the same based on the given dialouge. \n\nNote:\n- If the user asks to reschedule the call, or asks to callback later or denies to talk, then it is NOT a Glitch.\n- If there is a voice mail that was talking, then that is not a glitch. It is a machine stating that the call is deliberately cut, hence it is not a glitch.\n- If there is no user log and only agent spoke, then it is SURELY a GLITCH.\n\nExamples of a conversation having glitch:\n\nExample1:\n**\n2025-04-Sa 13:29:56 - agent: Hello Ma'am, I am <PERSON><PERSON>, an AI Assisstant from satya sunkulp Developers. May I confirm, am I speaking with Mrs. <PERSON><PERSON><PERSON>?\n2025-04-Sa 13:30:18 - user: Yes. Hello, <PERSON><PERSON>, are you there?\n2025-04-Sa 13:30:18 - agent: Yes, Ma'am, I am here. As per your form, you're looking for a good three BHK flat in Naroda, right?\n**\n\nExample2:\n**\n2025-04-Sa 22:39:07 - agent: Hello Sir, I am <PERSON><PERSON>, an AI Assisstant from satya sunkulp Developers. May I confirm, am I speaking with Mr. <PERSON>hul?\n2025-04-Sa 22:39:34 - user: Yes, please. Yes, please.\n2025-04-Sa 22:39:36 - agent: Glad to connect with you Sir! As per your response in our form, you're looking for three bedroom, hall, and kitchen flats in Naroda, right?\nThe Naroda Lavish scheme in Naroda offers three and four bedroom luxurious apartments. Would you like to know more about it?\n**\n\nExample3:\n**\n2025-04-Mo 12:51:52 - agent: Hello Sir, I am Priya, an AI Assisstant from satya sunkulp Developers. May I confirm, am I speaking with Mr. Anavart?\n**\n\nGive your output in JSON Format in the exact same format as shown:\n\n{\n\"Glitch\": \"Yes/No (either 'Yes' or 'No')\",\n\"Reason\": \"<Write why do you feel, there was a glitch>\"\n}\n\nOnly give the above as the output without any extra comment or sentences.", "imageDetail": "auto"}], "max_tokens": "2048", "temperature": "1", "n_completions": "1", "response_format": "text"}, "metadata": {"designer": {"x": -109, "y": 50}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-4o-mini (system)"}, "select": {"label": "Create a Chat Completion (GPT and o1 models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "User"}, "imageDetail": {"mode": "chose", "label": "Auto"}, "imageInputType": {"mode": "chose", "label": "Empty"}}]}, "logit_bias": {"mode": "chose"}, "response_format": {"mode": "chose", "label": "Text"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "My OpenAI connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": {"name": "value", "spec": [{"name": "token", "type": "text", "label": "Token ID", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "collection", "label": "Token Probability"}, "type": "array", "label": "Token Probability"}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "stop", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": {"name": "value", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "collection", "label": "Input Parameter"}, "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "max_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Max Completion Tokens"}, {"name": "messages", "spec": {"name": "value", "spec": [{"name": "role", "type": "select", "label": "Role", "options": {"store": [{"label": "User", "value": "user", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}, {"name": "imageInputType", "type": "select", "label": "Image Input Type", "options": [{"label": "URL", "value": "url", "nested": [{"help": "Make sure to use a publicly accessible URL.\nYou can test if your image is publicly accessible by opening the link in an incognito tab.", "name": "imageUrl", "type": "url", "label": "Image URL"}]}, {"label": "Image File", "value": "file", "nested": [{"name": "imageFile", "spec": [{"help": "Accepted extensions: `.jpg`, `.jpeg`, `.png`, `.webp` and `.gif`.", "name": "imageFilename", "type": "filename", "label": "Image Filename", "semantic": "file:name", "extension": ["jpg", "jpeg", "png", "webp", "gif"]}, {"name": "imageData", "type": "buffer", "label": "Image Data", "semantic": "file:data"}], "type": "collection", "label": "Image"}]}], "mappable": false}, {"help": "Recommended value: `Auto`", "name": "imageDetail", "type": "select", "label": "Image Detail", "options": [{"label": "Auto", "value": "auto", "default": true}, {"label": "High", "value": "high"}, {"label": "Low", "value": "low"}]}]}, {"label": "Assistant", "value": "assistant", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "Developer / System", "value": "system", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}]}, "required": true}], "type": "collection", "label": "Message"}, "type": "array", "label": "Messages", "required": true}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}, {"name": "prediction", "type": "text", "label": "Predicted Outputs"}], "interface": [{"name": "result", "type": "any", "label": "Result"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "object", "type": "text", "label": "Object"}, {"name": "created", "type": "date", "label": "Created"}, {"name": "model", "type": "text", "label": "Model"}, {"name": "choices", "spec": {"spec": [{"name": "text", "type": "text", "label": "Text"}, {"name": "index", "type": "number", "label": "Index"}, {"name": "logprobs", "type": "text", "label": "Log Probs"}, {"name": "finish_reason", "type": "text", "label": "Finish Reason"}, {"name": "message", "spec": [{"name": "role", "type": "text", "label": "Role"}, {"name": "content", "type": "text", "label": "Content"}, {"name": "refusal", "type": "text", "label": "Refusal"}], "type": "collection", "label": "Message"}], "type": "collection"}, "type": "array", "label": "Choices"}, {"name": "usage", "spec": [{"name": "prompt_tokens", "type": "number", "label": "Prompt Tokens"}, {"name": "completion_tokens", "type": "text", "label": "Completion Tokens"}, {"name": "total_tokens", "type": "number", "label": "Total Tokens"}, {"name": "prompt_tokens_details", "spec": [{"name": "cached_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "image_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Image Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Prompt Tokens Details"}, {"name": "completion_tokens_details", "spec": [{"name": "reasoning_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Reasoning Tokens"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}, {"name": "accepted_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Accepted Prediction Tokens"}, {"name": "rejected_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rejected Prediction Tokens"}], "type": "collection", "label": "Completion Tokens Details"}], "type": "collection", "label": "Usage"}, {"name": "service_tier", "type": "text", "label": "Service Tier"}, {"name": "system_fingerprint", "type": "text", "label": "System Fingerprint"}]}}, {"id": 42, "module": "json:ParseJSON", "version": 1, "parameters": {"type": ""}, "mapper": {"json": "{{38.result}}"}, "metadata": {"designer": {"x": 271, "y": 56}, "restore": {"parameters": {"type": {"label": "Choose a data structure"}}}, "parameters": [{"name": "type", "type": "udt", "label": "Data structure"}], "expect": [{"name": "json", "type": "text", "label": "JSON string", "required": true}]}}, {"id": 39, "module": "builtin:BasicRouter", "version": 1, "mapper": null, "metadata": {"designer": {"x": 609, "y": 46}}, "routes": [{"flow": [{"id": 17, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 7621612}, "mapper": {"model": "gpt-4o-mini", "top_p": "1", "select": "chat", "messages": [{"role": "user", "content": "This is the conversation dialouge between AI Voice Calling Agent from SatyaSankalp Developers and a client: {{9.text}}\n\nPlease output a Short 2 Line summary of the conversation in English stating the Interest of the client and also Tell <PERSON><PERSON> this is a cold Lead or a Hot Lead. A Lead is Hot if he or she has either booked a site visit or has asked relevant questions that the agent could not answer. Also mention in the requirement field, what is the user looking for, eg: \"2BHK\", \"3BHK\" or \"4BHK\" or \"2 & 3 BHK\" or \"3 & 4 BHK\" or Not Sure (note that in the requirement field, just output the flat type and no other comment/sentences).\n\nNote that the conversation provided to you may be improper as the speech to text model is not very accurate. Kindly use your intelligence if anything is unclear to you.\n\nGive your output in JSON Format in the exact same format as shown:\n\n{\n\"summary\": \"<Detailed Summary>\",\n\"lead_type\": \"<Hot/Cold, Choose any one and write it as \"Hot\" or \"Cold\">\",\n\"requirement\": \"<Flat Type the user is looking for>\"\n}\n\nPlease Note: DO NOT mention the json tag (```json ```). Just simply output as text starting with the brackets {}\n\nOnly give the above as the output without any extra comment or sentences.", "imageDetail": "auto"}], "max_tokens": "2048", "temperature": "1", "n_completions": "1", "response_format": "text"}, "metadata": {"designer": {"x": 1029, "y": 41}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-4o-mini (system)"}, "select": {"label": "Create a Chat Completion (GPT and o1 models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "User"}, "imageDetail": {"mode": "chose", "label": "Auto"}, "imageInputType": {"mode": "chose", "label": "Empty"}}]}, "logit_bias": {"mode": "chose"}, "response_format": {"mode": "chose", "label": "Text"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "My OpenAI connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": {"name": "value", "spec": [{"name": "token", "type": "text", "label": "Token ID", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "collection", "label": "Token Probability"}, "type": "array", "label": "Token Probability"}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "stop", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": {"name": "value", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "collection", "label": "Input Parameter"}, "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "max_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Max Completion Tokens"}, {"name": "messages", "spec": {"name": "value", "spec": [{"name": "role", "type": "select", "label": "Role", "options": {"store": [{"label": "User", "value": "user", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}, {"name": "imageInputType", "type": "select", "label": "Image Input Type", "options": [{"label": "URL", "value": "url", "nested": [{"help": "Make sure to use a publicly accessible URL.\nYou can test if your image is publicly accessible by opening the link in an incognito tab.", "name": "imageUrl", "type": "url", "label": "Image URL"}]}, {"label": "Image File", "value": "file", "nested": [{"name": "imageFile", "spec": [{"help": "Accepted extensions: `.jpg`, `.jpeg`, `.png`, `.webp` and `.gif`.", "name": "imageFilename", "type": "filename", "label": "Image Filename", "semantic": "file:name", "extension": ["jpg", "jpeg", "png", "webp", "gif"]}, {"name": "imageData", "type": "buffer", "label": "Image Data", "semantic": "file:data"}], "type": "collection", "label": "Image"}]}], "mappable": false}, {"help": "Recommended value: `Auto`", "name": "imageDetail", "type": "select", "label": "Image Detail", "options": [{"label": "Auto", "value": "auto", "default": true}, {"label": "High", "value": "high"}, {"label": "Low", "value": "low"}]}]}, {"label": "Assistant", "value": "assistant", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "Developer / System", "value": "system", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}]}, "required": true}], "type": "collection", "label": "Message"}, "type": "array", "label": "Messages", "required": true}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}, {"name": "prediction", "type": "text", "label": "Predicted Outputs"}], "interface": [{"name": "result", "type": "any", "label": "Result"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "object", "type": "text", "label": "Object"}, {"name": "created", "type": "date", "label": "Created"}, {"name": "model", "type": "text", "label": "Model"}, {"name": "choices", "spec": {"spec": [{"name": "text", "type": "text", "label": "Text"}, {"name": "index", "type": "number", "label": "Index"}, {"name": "logprobs", "type": "text", "label": "Log Probs"}, {"name": "finish_reason", "type": "text", "label": "Finish Reason"}, {"name": "message", "spec": [{"name": "role", "type": "text", "label": "Role"}, {"name": "content", "type": "text", "label": "Content"}, {"name": "refusal", "type": "text", "label": "Refusal"}], "type": "collection", "label": "Message"}], "type": "collection"}, "type": "array", "label": "Choices"}, {"name": "usage", "spec": [{"name": "prompt_tokens", "type": "number", "label": "Prompt Tokens"}, {"name": "completion_tokens", "type": "text", "label": "Completion Tokens"}, {"name": "total_tokens", "type": "number", "label": "Total Tokens"}, {"name": "prompt_tokens_details", "spec": [{"name": "cached_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "image_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Image Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Prompt Tokens Details"}, {"name": "completion_tokens_details", "spec": [{"name": "reasoning_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Reasoning Tokens"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}, {"name": "accepted_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Accepted Prediction Tokens"}, {"name": "rejected_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rejected Prediction Tokens"}], "type": "collection", "label": "Completion Tokens Details"}], "type": "collection", "label": "Usage"}, {"name": "service_tier", "type": "text", "label": "Service Tier"}, {"name": "system_fingerprint", "type": "text", "label": "System Fingerprint"}]}}, {"id": 23, "module": "json:ParseJSON", "version": 1, "parameters": {"type": ""}, "mapper": {"json": "{{17.result}}"}, "metadata": {"designer": {"x": 1335, "y": 27}, "restore": {"parameters": {"type": {"label": "Choose a data structure"}}}, "parameters": [{"name": "type", "type": "udt", "label": "Data structure"}], "expect": [{"name": "json", "type": "text", "label": "JSON string", "required": true}]}}, {"id": 52, "module": "json:TransformToJSON", "version": 1, "parameters": {"space": ""}, "mapper": {"object": "{{23.summary}}"}, "metadata": {"designer": {"x": 1649, "y": 56, "name": "JSON for Summary"}, "restore": {"parameters": {"space": {"label": "Empty"}}}, "parameters": [{"name": "space", "type": "select", "label": "Indentation", "validate": {"enum": ["tab", "2", "4"]}}], "expect": [{"name": "object", "type": "any", "label": "Object"}]}}, {"id": 8, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 7621612}, "mapper": {"model": "gpt-4o-mini", "top_p": "1", "select": "chat", "messages": [{"role": "user", "content": "Summarise the following conversation between an AI Sales Agent and a client:\n{{9.text}}.\n\nJust output at what time the user wants to book the visit and at what day (Format:YYYY-MM-DDTHH:MM:SS) . \n\nIf you cannot find that data, output the number: 0", "imageDetail": "auto"}], "max_tokens": "2048", "temperature": "1", "n_completions": "1", "response_format": "text"}, "metadata": {"designer": {"x": 2054, "y": 58}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-4o-mini (system)"}, "select": {"label": "Create a Chat Completion (GPT and o1 models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "User"}, "imageDetail": {"mode": "chose", "label": "Auto"}, "imageInputType": {"mode": "chose", "label": "Empty"}}]}, "logit_bias": {"mode": "chose"}, "response_format": {"mode": "chose", "label": "Text"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "My OpenAI connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": {"name": "value", "spec": [{"name": "token", "type": "text", "label": "Token ID", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "collection", "label": "Token Probability"}, "type": "array", "label": "Token Probability"}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "stop", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": {"name": "value", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "collection", "label": "Input Parameter"}, "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "max_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Max Completion Tokens"}, {"name": "messages", "spec": {"name": "value", "spec": [{"name": "role", "type": "select", "label": "Role", "options": {"store": [{"label": "User", "value": "user", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}, {"name": "imageInputType", "type": "select", "label": "Image Input Type", "options": [{"label": "URL", "value": "url", "nested": [{"help": "Make sure to use a publicly accessible URL.\nYou can test if your image is publicly accessible by opening the link in an incognito tab.", "name": "imageUrl", "type": "url", "label": "Image URL"}]}, {"label": "Image File", "value": "file", "nested": [{"name": "imageFile", "spec": [{"help": "Accepted extensions: `.jpg`, `.jpeg`, `.png`, `.webp` and `.gif`.", "name": "imageFilename", "type": "filename", "label": "Image Filename", "semantic": "file:name", "extension": ["jpg", "jpeg", "png", "webp", "gif"]}, {"name": "imageData", "type": "buffer", "label": "Image Data", "semantic": "file:data"}], "type": "collection", "label": "Image"}]}], "mappable": false}, {"help": "Recommended value: `Auto`", "name": "imageDetail", "type": "select", "label": "Image Detail", "options": [{"label": "Auto", "value": "auto", "default": true}, {"label": "High", "value": "high"}, {"label": "Low", "value": "low"}]}]}, {"label": "Assistant", "value": "assistant", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "Developer / System", "value": "system", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}]}, "required": true}], "type": "collection", "label": "Message"}, "type": "array", "label": "Messages", "required": true}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}, {"name": "prediction", "type": "text", "label": "Predicted Outputs"}], "interface": [{"name": "result", "type": "any", "label": "Result"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "object", "type": "text", "label": "Object"}, {"name": "created", "type": "date", "label": "Created"}, {"name": "model", "type": "text", "label": "Model"}, {"name": "choices", "spec": {"spec": [{"name": "text", "type": "text", "label": "Text"}, {"name": "index", "type": "number", "label": "Index"}, {"name": "logprobs", "type": "text", "label": "Log Probs"}, {"name": "finish_reason", "type": "text", "label": "Finish Reason"}, {"name": "message", "spec": [{"name": "role", "type": "text", "label": "Role"}, {"name": "content", "type": "text", "label": "Content"}, {"name": "refusal", "type": "text", "label": "Refusal"}], "type": "collection", "label": "Message"}], "type": "collection"}, "type": "array", "label": "Choices"}, {"name": "usage", "spec": [{"name": "prompt_tokens", "type": "number", "label": "Prompt Tokens"}, {"name": "completion_tokens", "type": "text", "label": "Completion Tokens"}, {"name": "total_tokens", "type": "number", "label": "Total Tokens"}, {"name": "prompt_tokens_details", "spec": [{"name": "cached_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "image_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Image Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Prompt Tokens Details"}, {"name": "completion_tokens_details", "spec": [{"name": "reasoning_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Reasoning Tokens"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}, {"name": "accepted_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Accepted Prediction Tokens"}, {"name": "rejected_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rejected Prediction Tokens"}], "type": "collection", "label": "Completion Tokens Details"}], "type": "collection", "label": "Usage"}, {"name": "service_tier", "type": "text", "label": "Service Tier"}, {"name": "system_fingerprint", "type": "text", "label": "System Fingerprint"}]}}, {"id": 44, "module": "util:<PERSON>witcher", "version": 1, "parameters": {}, "mapper": {"input": "{{8.result}}", "casesTable": [{"output": "-", "pattern": "0"}], "elseOutput": "{{formatDate(addMinutes(8.result; 0); \"dddd, DD MMMM YYYY hh:mm A\")}}", "useRegExpMatch": false}, "metadata": {"designer": {"x": 2344, "y": 65}, "restore": {"expect": {"casesTable": {"mode": "chose", "items": [null]}}}, "expect": [{"name": "input", "type": "text", "label": "Input"}, {"name": "useRegExpMatch", "type": "boolean", "label": "Use regular expressions to match", "required": true}, {"name": "casesTable", "spec": [{"name": "pattern", "type": "text", "label": "Pattern"}, {"name": "output", "type": "any", "label": "Output"}], "type": "array", "label": "Cases", "required": true}, {"name": "elseOutput", "type": "any", "label": "Else"}]}}, {"id": 45, "module": "util:<PERSON>witcher", "version": 1, "parameters": {}, "mapper": {"input": "{{8.result}}", "casesTable": [{"output": "No", "pattern": "0"}], "elseOutput": "Yes", "useRegExpMatch": false}, "metadata": {"designer": {"x": 2767, "y": 50}, "restore": {"expect": {"casesTable": {"mode": "chose", "items": [null]}}}, "expect": [{"name": "input", "type": "text", "label": "Input"}, {"name": "useRegExpMatch", "type": "boolean", "label": "Use regular expressions to match", "required": true}, {"name": "casesTable", "spec": [{"name": "pattern", "type": "text", "label": "Pattern"}, {"name": "output", "type": "any", "label": "Output"}], "type": "array", "label": "Cases", "required": true}, {"name": "elseOutput", "type": "any", "label": "Else"}]}}, {"id": 24, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 7621612}, "mapper": {"model": "gpt-4o-mini", "top_p": "1", "select": "chat", "messages": [{"role": "user", "content": "This is the conversation dialouge between AI Voice Calling Agent from SatyaSankalp Developers and a client: {{9.text}}\n\nPlease output wether the call needs immediate Sales Team Attention (Yes if there are queries that were not answered by the AI Agent, No otherwise). If yes, why and what extra information the user was seeking for that the agent could not answer (but in brief). If No, just give why No (but in brief).\n\nNote that the conversation provided to you may be improper as the speech to text model is not very accurate. Kindly use your intelligence if anything is unclear to you but do not keep any placeholders in your response, just fill them with your own intelligence. And never write anything negative about the AI Agent.\n\nGive your output in JSON Format in the exact same format as shown:\n\n{\n\"need_attention\": \"<Yes/No>\",\n\"reason\": \"<Mention the reason here as discussed above in brief>\"\n}\n\nOnly give the above as the output without any extra comment or sentences.", "imageDetail": "auto"}], "max_tokens": "2048", "temperature": "1", "n_completions": "1", "response_format": "text"}, "metadata": {"designer": {"x": 3261, "y": 65}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-4o-mini (system)"}, "select": {"label": "Create a Chat Completion (GPT and o1 models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "User"}, "imageDetail": {"mode": "chose", "label": "Auto"}, "imageInputType": {"mode": "chose", "label": "Empty"}}]}, "logit_bias": {"mode": "chose"}, "response_format": {"mode": "chose", "label": "Text"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "My OpenAI connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": {"name": "value", "spec": [{"name": "token", "type": "text", "label": "Token ID", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "collection", "label": "Token Probability"}, "type": "array", "label": "Token Probability"}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "stop", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": {"name": "value", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "collection", "label": "Input Parameter"}, "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "max_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Max Completion Tokens"}, {"name": "messages", "spec": {"name": "value", "spec": [{"name": "role", "type": "select", "label": "Role", "options": {"store": [{"label": "User", "value": "user", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}, {"name": "imageInputType", "type": "select", "label": "Image Input Type", "options": [{"label": "URL", "value": "url", "nested": [{"help": "Make sure to use a publicly accessible URL.\nYou can test if your image is publicly accessible by opening the link in an incognito tab.", "name": "imageUrl", "type": "url", "label": "Image URL"}]}, {"label": "Image File", "value": "file", "nested": [{"name": "imageFile", "spec": [{"help": "Accepted extensions: `.jpg`, `.jpeg`, `.png`, `.webp` and `.gif`.", "name": "imageFilename", "type": "filename", "label": "Image Filename", "semantic": "file:name", "extension": ["jpg", "jpeg", "png", "webp", "gif"]}, {"name": "imageData", "type": "buffer", "label": "Image Data", "semantic": "file:data"}], "type": "collection", "label": "Image"}]}], "mappable": false}, {"help": "Recommended value: `Auto`", "name": "imageDetail", "type": "select", "label": "Image Detail", "options": [{"label": "Auto", "value": "auto", "default": true}, {"label": "High", "value": "high"}, {"label": "Low", "value": "low"}]}]}, {"label": "Assistant", "value": "assistant", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "Developer / System", "value": "system", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}]}, "required": true}], "type": "collection", "label": "Message"}, "type": "array", "label": "Messages", "required": true}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}, {"name": "prediction", "type": "text", "label": "Predicted Outputs"}], "interface": [{"name": "result", "type": "any", "label": "Result"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "object", "type": "text", "label": "Object"}, {"name": "created", "type": "date", "label": "Created"}, {"name": "model", "type": "text", "label": "Model"}, {"name": "choices", "spec": {"spec": [{"name": "text", "type": "text", "label": "Text"}, {"name": "index", "type": "number", "label": "Index"}, {"name": "logprobs", "type": "text", "label": "Log Probs"}, {"name": "finish_reason", "type": "text", "label": "Finish Reason"}, {"name": "message", "spec": [{"name": "role", "type": "text", "label": "Role"}, {"name": "content", "type": "text", "label": "Content"}, {"name": "refusal", "type": "text", "label": "Refusal"}], "type": "collection", "label": "Message"}], "type": "collection"}, "type": "array", "label": "Choices"}, {"name": "usage", "spec": [{"name": "prompt_tokens", "type": "number", "label": "Prompt Tokens"}, {"name": "completion_tokens", "type": "text", "label": "Completion Tokens"}, {"name": "total_tokens", "type": "number", "label": "Total Tokens"}, {"name": "prompt_tokens_details", "spec": [{"name": "cached_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "image_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Image Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Prompt Tokens Details"}, {"name": "completion_tokens_details", "spec": [{"name": "reasoning_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Reasoning Tokens"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}, {"name": "accepted_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Accepted Prediction Tokens"}, {"name": "rejected_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rejected Prediction Tokens"}], "type": "collection", "label": "Completion Tokens Details"}], "type": "collection", "label": "Usage"}, {"name": "service_tier", "type": "text", "label": "Service Tier"}, {"name": "system_fingerprint", "type": "text", "label": "System Fingerprint"}]}}, {"id": 25, "module": "json:ParseJSON", "version": 1, "parameters": {"type": ""}, "mapper": {"json": "{{24.result}}"}, "metadata": {"designer": {"x": 3731, "y": 41}, "restore": {"parameters": {"type": {"label": "Choose a data structure"}}}, "parameters": [{"name": "type", "type": "udt", "label": "Data structure"}], "expect": [{"name": "json", "type": "text", "label": "JSON string", "required": true}]}}, {"id": 53, "module": "json:TransformToJSON", "version": 1, "parameters": {"space": ""}, "mapper": {"object": "{{25.reason}}"}, "metadata": {"designer": {"x": 4036, "y": 64, "name": "JSON for Reason of Transfer"}, "restore": {"parameters": {"space": {"label": "Empty"}}}, "parameters": [{"name": "space", "type": "select", "label": "Indentation", "validate": {"enum": ["tab", "2", "4"]}}], "expect": [{"name": "object", "type": "any", "label": "Object"}]}}, {"id": 43, "module": "util:<PERSON>witcher", "version": 1, "parameters": {}, "mapper": {"input": "{{1.call_transferred}}", "casesTable": [{"output": "Yes", "pattern": "true"}, {"output": "No", "pattern": "false"}], "elseOutput": "-", "useRegExpMatch": false}, "metadata": {"designer": {"x": 4280, "y": 56}, "restore": {"expect": {"casesTable": {"mode": "chose", "items": [null, null]}}}, "expect": [{"name": "input", "type": "text", "label": "Input"}, {"name": "useRegExpMatch", "type": "boolean", "label": "Use regular expressions to match", "required": true}, {"name": "casesTable", "spec": [{"name": "pattern", "type": "text", "label": "Pattern"}, {"name": "output", "type": "any", "label": "Output"}], "type": "array", "label": "Cases", "required": true}, {"name": "elseOutput", "type": "any", "label": "Else"}]}}, {"id": 47, "module": "google-sheets:updateRow", "version": 2, "parameters": {"__IMTCONN__": 7621135}, "mapper": {"from": "drive", "mode": "select", "values": {"6": "{{1.dial_status}}", "7": "{{9.text}}", "8": "{{23.summary}}", "9": "{{23.lead_type}}", "10": "{{45.output}}", "11": "{{44.output}}", "12": "{{25.need_attention}}", "13": "{{25.reason}}", "14": "{{42.<PERSON>litch}}", "15": "{{34.output}}"}, "sheetId": "Updated Format of Form Responses", "rowNumber": "{{20.`__ROW_NUMBER__`}}", "spreadsheetId": "/1I1zy3OaEj2qEZTTJVE8raTi6c_nfvNDhf6OnFvJ5h_Y", "includesHeaders": true, "valueInputOption": "USER_ENTERED"}, "metadata": {"designer": {"x": 4558, "y": 51}, "restore": {"expect": {"from": {"label": "My Drive"}, "mode": {"label": "Search by path", "collapsed": true}, "sheetId": {"label": "Updated Format of Form Responses"}, "spreadsheetId": {"path": ["<PERSON><PERSON>a <PERSON>p Client Data"]}, "includesHeaders": {"label": "Yes", "nested": [{"name": "values", "spec": [{"name": "0", "type": "text", "label": "Timestamp (A)"}, {"name": "1", "type": "text", "label": "First Name (B)"}, {"name": "2", "type": "text", "label": "Last Name (C)"}, {"name": "3", "type": "text", "label": "Contact No. (D)"}, {"name": "4", "type": "text", "label": "Project (E)"}, {"name": "5", "type": "text", "label": "Unique Id (F)"}, {"name": "6", "type": "text", "label": "Client Call Connection Status (G)"}, {"name": "7", "type": "text", "label": "Full Call Conversation (H)"}, {"name": "8", "type": "text", "label": "Call Summary (I)"}, {"name": "9", "type": "text", "label": "Lead Type (Hot/Cold) (J)"}, {"name": "10", "type": "text", "label": "Site Visit Booked (K)"}, {"name": "11", "type": "text", "label": "Visit Booking Date and Time (L)"}, {"name": "12", "type": "text", "label": "Does this user needs Immediate call from the Sales Team (M)"}, {"name": "13", "type": "text", "label": "Reason for Yes/No for attention from the Sales Team (N)"}, {"name": "14", "type": "text", "label": "Was there a glitch in the final Call (O)"}, {"name": "15", "type": "text", "label": "Number of Times Call Tried (Agent to Client) (P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}], "type": "collection", "label": "Values"}]}, "valueInputOption": {"mode": "chose", "label": "User entered"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "mode", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["select", "fromAll", "map"]}}, {"name": "valueInputOption", "type": "select", "label": "Value input option", "validate": {"enum": ["USER_ENTERED", "RAW"]}}, {"name": "from", "type": "select", "label": "Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "spreadsheetId", "type": "file", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "rowNumber", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Row number", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "values", "spec": [{"name": "0", "type": "text", "label": "Timestamp (A)"}, {"name": "1", "type": "text", "label": "First Name (B)"}, {"name": "2", "type": "text", "label": "Last Name (C)"}, {"name": "3", "type": "text", "label": "Contact No. (D)"}, {"name": "4", "type": "text", "label": "Project (E)"}, {"name": "5", "type": "text", "label": "Unique Id (F)"}, {"name": "6", "type": "text", "label": "Client Call Connection Status (G)"}, {"name": "7", "type": "text", "label": "Full Call Conversation (H)"}, {"name": "8", "type": "text", "label": "Call Summary (I)"}, {"name": "9", "type": "text", "label": "Lead Type (Hot/Cold) (J)"}, {"name": "10", "type": "text", "label": "Site Visit Booked (K)"}, {"name": "11", "type": "text", "label": "Visit Booking Date and Time (L)"}, {"name": "12", "type": "text", "label": "Does this user needs Immediate call from the Sales Team (M)"}, {"name": "13", "type": "text", "label": "Reason for Yes/No for attention from the Sales Team (N)"}, {"name": "14", "type": "text", "label": "Was there a glitch in the final Call (O)"}, {"name": "15", "type": "text", "label": "Number of Times Call Tried (Agent to Client) (P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}], "type": "collection", "label": "Values"}]}}, {"id": 50, "module": "http:ActionSendData", "version": 3, "parameters": {"handleErrors": true, "useNewZLibDeCompress": true}, "mapper": {"ca": "", "qs": [], "url": "https://client1.phlexibit.com/api/create-lead", "data": "{\n    \"first_name\": \"{{20.`1`}}\",\n    \"last_name\": \"{{ifempty(20.`2`; )}}\",\n    \"google_sheet_id\": \"{{20.`5`}}\",\n    \"contact_number\": \"+{{20.`3`}}\",\n    \"requirement\": \"{{23.requirement}}\",\n    \"call_status\": \"{{1.dial_status}}\",\n    \"projectName\": \"Naroda Lavish\",\n    \"full_conversation\": {{51.json}},\n    \"call_summary\": {{52.json}},\n    \"is_hot_lead\":{{if(23.lead_type = \"Hot\"; \"true\"; \"false\")}},\n    \"reached\":false,\n    \"transfered\": {{if(25.need_attention = \"Yes\" & 23.lead_type = \"Hot\"; \"true\"; \"false\")}},\n    \"site_visit\": \"{{8.result}}\",\n\"reason_of_transfer\": {{53.json}}\n}", "gzip": true, "method": "post", "headers": [{"name": "Content-Type", "value": "application/json"}], "timeout": "", "useMtls": false, "authPass": "", "authUser": "", "bodyType": "raw", "contentType": "application/json", "serializeUrl": false, "shareCookies": false, "parseResponse": true, "followRedirect": true, "useQuerystring": false, "followAllRedirects": false, "rejectUnauthorized": true}, "metadata": {"designer": {"x": 4919, "y": 44}, "restore": {"expect": {"qs": {"mode": "chose"}, "method": {"mode": "chose", "label": "POST"}, "headers": {"mode": "chose", "items": [null]}, "bodyType": {"label": "Raw"}, "contentType": {"label": "JSON (application/json)"}}}, "parameters": [{"name": "handleErrors", "type": "boolean", "label": "Evaluate all states as errors (except for 2xx and 3xx )", "required": true}, {"name": "useNewZLibDeCompress", "type": "hidden"}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "serializeUrl", "type": "boolean", "label": "Serialize URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["get", "head", "post", "put", "patch", "delete", "options"]}}, {"name": "headers", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Headers"}, {"name": "qs", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Query String"}, {"name": "bodyType", "type": "select", "label": "Body type", "validate": {"enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]}}, {"name": "parseResponse", "type": "boolean", "label": "Parse response", "required": true}, {"name": "authUser", "type": "text", "label": "User name"}, {"name": "authPass", "type": "password", "label": "Password"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout", "validate": {"max": 300, "min": 1}}, {"name": "shareCookies", "type": "boolean", "label": "Share cookies with other HTTP modules", "required": true}, {"name": "ca", "type": "cert", "label": "Self-signed certificate"}, {"name": "rejectUnauthorized", "type": "boolean", "label": "Reject connections that are using unverified (self-signed) certificates", "required": true}, {"name": "followRedirect", "type": "boolean", "label": "Follow redirect", "required": true}, {"name": "useQuerystring", "type": "boolean", "label": "Disable serialization of multiple same query string keys as arrays", "required": true}, {"name": "gzip", "type": "boolean", "label": "Request compressed content", "required": true}, {"name": "useMtls", "type": "boolean", "label": "Use Mutual TLS", "required": true}, {"name": "contentType", "type": "select", "label": "Content type", "validate": {"enum": ["text/plain", "application/json", "application/xml", "text/xml", "text/html", "custom"]}}, {"name": "data", "type": "buffer", "label": "Request content"}, {"name": "followAllRedirects", "type": "boolean", "label": "Follow all redirect", "required": true}]}}]}]}]}]}], "metadata": {"instant": true, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": [[{"id": 55, "module": "http:ActionSendData", "version": 3, "parameters": {"handleErrors": true, "useNewZLibDeCompress": true}, "filter": {"name": "If Site Visit Booked", "conditions": [[{"a": "{{8.result}}", "b": "0", "o": "text:notequal"}]]}, "mapper": {"ca": "", "qs": [], "url": "https://client1.phlexibit.com/forward-inquiry-followup", "data": "{\n\"inquiryId\": \"{{1.Unique_id}}\",\n\"action\": \"VISIT\",\n\"datetime\": \"{{if(8.result = 0; \"0000-00-00 00:00\"; formatDate(8.result; \"YYYY-MM-DD HH:mm\"))}}\",\n\"note\":\"{{if(45.output = \"Yes\"; \"Visit has been scheduled\"; \"Visit not scheduled\")}}\"\n}", "gzip": true, "method": "post", "headers": [{"name": "Content-Type", "value": "application/json"}], "timeout": "", "useMtls": false, "authPass": "", "authUser": "", "bodyType": "raw", "contentType": "application/json", "serializeUrl": false, "shareCookies": false, "parseResponse": false, "followRedirect": true, "useQuerystring": false, "followAllRedirects": false, "rejectUnauthorized": true}, "metadata": {"designer": {"x": 5286, "y": 46, "messages": [{"category": "link", "severity": "warning", "message": "The module is not connected to the data flow."}, {"category": "reference", "severity": "warning", "message": "Referenced module 'Webhooks - Custom webhook' [1] is not accessible."}, {"category": "reference", "severity": "warning", "message": "Referenced module 'OpenAI (ChatGPT, Whisper, DALL-E) - Create a Completion (Prompt) (GPT and o-series Models)' [8] is not accessible."}, {"category": "reference", "severity": "warning", "message": "Referenced module 'Tools - Switch' [45] is not accessible."}]}, "restore": {"expect": {"qs": {"mode": "chose"}, "method": {"mode": "chose", "label": "POST"}, "headers": {"mode": "chose", "items": [null]}, "bodyType": {"label": "Raw"}, "contentType": {"label": "JSON (application/json)"}}}, "parameters": [{"name": "handleErrors", "type": "boolean", "label": "Evaluate all states as errors (except for 2xx and 3xx )", "required": true}, {"name": "useNewZLibDeCompress", "type": "hidden"}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "serializeUrl", "type": "boolean", "label": "Serialize URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["get", "head", "post", "put", "patch", "delete", "options"]}}, {"name": "headers", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Headers"}, {"name": "qs", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Query String"}, {"name": "bodyType", "type": "select", "label": "Body type", "validate": {"enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]}}, {"name": "parseResponse", "type": "boolean", "label": "Parse response", "required": true}, {"name": "authUser", "type": "text", "label": "User name"}, {"name": "authPass", "type": "password", "label": "Password"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout", "validate": {"max": 300, "min": 1}}, {"name": "shareCookies", "type": "boolean", "label": "Share cookies with other HTTP modules", "required": true}, {"name": "ca", "type": "cert", "label": "Self-signed certificate"}, {"name": "rejectUnauthorized", "type": "boolean", "label": "Reject connections that are using unverified (self-signed) certificates", "required": true}, {"name": "followRedirect", "type": "boolean", "label": "Follow redirect", "required": true}, {"name": "useQuerystring", "type": "boolean", "label": "Disable serialization of multiple same query string keys as arrays", "required": true}, {"name": "gzip", "type": "boolean", "label": "Request compressed content", "required": true}, {"name": "useMtls", "type": "boolean", "label": "Use Mutual TLS", "required": true}, {"name": "contentType", "type": "select", "label": "Content type", "validate": {"enum": ["text/plain", "application/json", "application/xml", "text/xml", "text/html", "custom"]}}, {"name": "data", "type": "buffer", "label": "Request content"}, {"name": "followAllRedirects", "type": "boolean", "label": "Follow all redirect", "required": true}]}}]]}, "zone": "eu2.make.com", "notes": []}}