"""
Configuration settings for the FastAPI application
"""

from pydantic_settings import BaseSettings
from typing import List
import os


class Settings(BaseSettings):
    """Application settings"""
    
    # Application settings
    APP_NAME: str = "Integration Webhooks API"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # MongoDB settings
    MONGODB_URL: str = "mongodb://localhost:27017"
    DATABASE_NAME: str = "integration_webhooks"
    
    # Security settings
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # CORS settings
    ALLOWED_ORIGINS: List[str] = ["*"]
    
    # Webhook settings
    WEBHOOK_SECRET: str = "webhook-secret-key"
    MAX_WEBHOOK_RETRIES: int = 3
    
    # Logging settings
    LOG_LEVEL: str = "INFO"
    
    # Pagination settings
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()


# MongoDB Collection Names
class Collections:
    CALL_LOGS = "call_logs"
    CONVERSATIONS = "conversations"
    FORM_RESPONSES = "form_responses"
    WEBHOOK_LOGS = "webhook_logs"


# Field mappings from Google Sheets to MongoDB
CALL_LOG_FIELDS = {
    "unique_id": "A",
    "call_start_time": "B", 
    "call_end_time": "C",
    "call_duration_sec": "D",
    "call_duration_min": "E",
    # Add more field mappings as needed
}

FORM_RESPONSE_FIELDS = {
    "unique_id": "F",
    # Add more field mappings as needed
}
