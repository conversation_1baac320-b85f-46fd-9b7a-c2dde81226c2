"""
Export service for data export functionality
Replaces Google Sheets export functionality
"""

from motor.motor_asyncio import AsyncIOMotorDatabase
from datetime import datetime
from typing import Optional, Dict, Any, List
import logging
import json
import csv
import io
from openpyxl import Workbook

from app.models import ExportRequest
from app.config import Collections
from app.services.call_log_service import CallLogService
from app.services.form_response_service import FormResponseService
from app.services.conversation_service import ConversationService

logger = logging.getLogger(__name__)


class ExportService:
    """Service for export operations"""
    
    def __init__(self, database: AsyncIOMotorDatabase):
        self.db = database
        self.call_log_service = CallLogService(database)
        self.form_response_service = FormResponseService(database)
        self.conversation_service = ConversationService(database)
    
    async def export_call_logs(
        self, 
        format: str = "json", 
        filters: Optional[Dict[str, Any]] = None
    ) -> bytes:
        """Export call logs data"""
        try:
            # Get all call logs (without pagination for export)
            result = await self.call_log_service.get_call_logs(
                page=1, 
                size=10000,  # Large number to get all records
                filters=filters
            )
            
            call_logs_data = [call_log.dict() for call_log in result.items]
            
            if format == "json":
                return json.dumps(call_logs_data, indent=2, default=str).encode('utf-8')
            
            elif format == "csv":
                return self._export_to_csv(call_logs_data, "call_logs")
            
            elif format == "xlsx":
                return self._export_to_xlsx(call_logs_data, "call_logs")
            
            else:
                raise ValueError(f"Unsupported format: {format}")
                
        except Exception as e:
            logger.error(f"Error exporting call logs: {e}")
            raise
    
    async def export_form_responses(
        self, 
        format: str = "json", 
        filters: Optional[Dict[str, Any]] = None
    ) -> bytes:
        """Export form responses data"""
        try:
            # Get all form responses (without pagination for export)
            result = await self.form_response_service.get_form_responses(
                page=1, 
                size=10000,  # Large number to get all records
                filters=filters
            )
            
            form_responses_data = [form_response.dict() for form_response in result.items]
            
            if format == "json":
                return json.dumps(form_responses_data, indent=2, default=str).encode('utf-8')
            
            elif format == "csv":
                return self._export_to_csv(form_responses_data, "form_responses")
            
            elif format == "xlsx":
                return self._export_to_xlsx(form_responses_data, "form_responses")
            
            else:
                raise ValueError(f"Unsupported format: {format}")
                
        except Exception as e:
            logger.error(f"Error exporting form responses: {e}")
            raise
    
    async def export_conversations(
        self, 
        format: str = "json", 
        filters: Optional[Dict[str, Any]] = None,
        include_transcripts: bool = False
    ) -> bytes:
        """Export conversations data"""
        try:
            # Get all conversations (without pagination for export)
            result = await self.conversation_service.get_conversations(
                page=1, 
                size=10000,  # Large number to get all records
                filters=filters
            )
            
            conversations_data = []
            for conversation in result.items:
                conv_dict = conversation.dict()
                if not include_transcripts:
                    # Remove large transcript fields to reduce size
                    conv_dict.pop("formatted_transcript", None)
                    conv_dict.pop("full_transcript", None)
                conversations_data.append(conv_dict)
            
            if format == "json":
                return json.dumps(conversations_data, indent=2, default=str).encode('utf-8')
            
            elif format == "csv":
                return self._export_to_csv(conversations_data, "conversations")
            
            elif format == "xlsx":
                return self._export_to_xlsx(conversations_data, "conversations")
            
            else:
                raise ValueError(f"Unsupported format: {format}")
                
        except Exception as e:
            logger.error(f"Error exporting conversations: {e}")
            raise
    
    async def export_custom_data(self, export_request: ExportRequest) -> bytes:
        """Export custom data based on request parameters"""
        try:
            # This would implement custom export logic based on the request
            # For now, we'll export all data types combined
            
            call_logs_result = await self.call_log_service.get_call_logs(
                page=1, size=10000, filters=export_request.filters
            )
            
            form_responses_result = await self.form_response_service.get_form_responses(
                page=1, size=10000, filters=export_request.filters
            )
            
            conversations_result = await self.conversation_service.get_conversations(
                page=1, size=10000, filters=export_request.filters
            )
            
            combined_data = {
                "call_logs": [item.dict() for item in call_logs_result.items],
                "form_responses": [item.dict() for item in form_responses_result.items],
                "conversations": [item.dict() for item in conversations_result.items],
                "export_metadata": {
                    "exported_at": datetime.utcnow().isoformat(),
                    "filters": export_request.filters,
                    "total_records": (
                        len(call_logs_result.items) + 
                        len(form_responses_result.items) + 
                        len(conversations_result.items)
                    )
                }
            }
            
            if export_request.format == "json":
                return json.dumps(combined_data, indent=2, default=str).encode('utf-8')
            
            elif export_request.format == "csv":
                # For CSV, we'll create separate sections
                return self._export_combined_to_csv(combined_data)
            
            elif export_request.format == "xlsx":
                # For XLSX, we'll create separate sheets
                return self._export_combined_to_xlsx(combined_data)
            
            else:
                raise ValueError(f"Unsupported format: {export_request.format}")
                
        except Exception as e:
            logger.error(f"Error exporting custom data: {e}")
            raise
    
    def _export_to_csv(self, data: List[Dict[str, Any]], data_type: str) -> bytes:
        """Export data to CSV format"""
        if not data:
            return b""
        
        output = io.StringIO()
        
        # Get all possible field names
        fieldnames = set()
        for item in data:
            fieldnames.update(self._flatten_dict(item).keys())
        
        fieldnames = sorted(list(fieldnames))
        
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        
        for item in data:
            flattened = self._flatten_dict(item)
            writer.writerow(flattened)
        
        return output.getvalue().encode('utf-8')
    
    def _export_to_xlsx(self, data: List[Dict[str, Any]], data_type: str) -> bytes:
        """Export data to XLSX format"""
        if not data:
            # Return empty workbook
            wb = Workbook()
            ws = wb.active
            ws.title = data_type
            output = io.BytesIO()
            wb.save(output)
            return output.getvalue()
        
        wb = Workbook()
        ws = wb.active
        ws.title = data_type
        
        # Get all possible field names
        fieldnames = set()
        for item in data:
            fieldnames.update(self._flatten_dict(item).keys())
        
        fieldnames = sorted(list(fieldnames))
        
        # Write headers
        for col, fieldname in enumerate(fieldnames, 1):
            ws.cell(row=1, column=col, value=fieldname)
        
        # Write data
        for row, item in enumerate(data, 2):
            flattened = self._flatten_dict(item)
            for col, fieldname in enumerate(fieldnames, 1):
                value = flattened.get(fieldname, "")
                ws.cell(row=row, column=col, value=str(value) if value is not None else "")
        
        output = io.BytesIO()
        wb.save(output)
        return output.getvalue()
    
    def _export_combined_to_csv(self, combined_data: Dict[str, Any]) -> bytes:
        """Export combined data to CSV with sections"""
        output = io.StringIO()
        
        for section_name, section_data in combined_data.items():
            if section_name == "export_metadata":
                continue
                
            if isinstance(section_data, list) and section_data:
                output.write(f"\n=== {section_name.upper()} ===\n")
                
                # Get fieldnames for this section
                fieldnames = set()
                for item in section_data:
                    fieldnames.update(self._flatten_dict(item).keys())
                
                fieldnames = sorted(list(fieldnames))
                
                writer = csv.DictWriter(output, fieldnames=fieldnames)
                writer.writeheader()
                
                for item in section_data:
                    flattened = self._flatten_dict(item)
                    writer.writerow(flattened)
                
                output.write("\n")
        
        return output.getvalue().encode('utf-8')
    
    def _export_combined_to_xlsx(self, combined_data: Dict[str, Any]) -> bytes:
        """Export combined data to XLSX with separate sheets"""
        wb = Workbook()
        
        # Remove default sheet
        wb.remove(wb.active)
        
        for section_name, section_data in combined_data.items():
            if section_name == "export_metadata":
                continue
                
            if isinstance(section_data, list):
                ws = wb.create_sheet(title=section_name)
                
                if section_data:
                    # Get fieldnames for this section
                    fieldnames = set()
                    for item in section_data:
                        fieldnames.update(self._flatten_dict(item).keys())
                    
                    fieldnames = sorted(list(fieldnames))
                    
                    # Write headers
                    for col, fieldname in enumerate(fieldnames, 1):
                        ws.cell(row=1, column=col, value=fieldname)
                    
                    # Write data
                    for row, item in enumerate(section_data, 2):
                        flattened = self._flatten_dict(item)
                        for col, fieldname in enumerate(fieldnames, 1):
                            value = flattened.get(fieldname, "")
                            ws.cell(row=row, column=col, value=str(value) if value is not None else "")
        
        # If no sheets were created, create an empty one
        if not wb.worksheets:
            wb.create_sheet(title="Empty")
        
        output = io.BytesIO()
        wb.save(output)
        return output.getvalue()
    
    def _flatten_dict(self, d: Dict[str, Any], parent_key: str = "", sep: str = ".") -> Dict[str, Any]:
        """Flatten nested dictionary"""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                # Convert list to string representation
                items.append((new_key, str(v)))
            else:
                items.append((new_key, v))
        return dict(items)
    
    async def get_export_templates(self) -> List[Dict[str, Any]]:
        """Get available export templates"""
        return [
            {
                "name": "call_logs_basic",
                "description": "Basic call logs export",
                "fields": ["unique_id", "call_start_time", "call_end_time", "call_duration_min"],
                "format": "csv"
            },
            {
                "name": "form_responses_complete",
                "description": "Complete form responses export",
                "fields": ["unique_id", "form_type", "response_data", "status", "created_at"],
                "format": "xlsx"
            },
            {
                "name": "conversations_summary",
                "description": "Conversation summary export",
                "fields": ["unique_id", "message_count", "created_at"],
                "format": "json"
            }
        ]
    
    async def get_export_status(self, export_id: str) -> Optional[Dict[str, Any]]:
        """Get export job status (placeholder for async exports)"""
        # This would be implemented for long-running export jobs
        # For now, return a placeholder
        return {
            "export_id": export_id,
            "status": "completed",
            "progress": 100,
            "created_at": datetime.utcnow().isoformat(),
            "completed_at": datetime.utcnow().isoformat()
        }
