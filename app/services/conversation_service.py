"""
Conversation service for managing conversation operations
Replaces BasicFeeder + TextAggregator + TransformToJSON functionality
"""

from motor.motor_asyncio import AsyncIOMotorDatabase
from datetime import datetime
from typing import Optional, Dict, Any, List
import logging
import json
import math

from app.models import (
    Conversation, ConversationCreate, ConversationMessage, 
    PaginatedResponse
)
from app.config import Collections

logger = logging.getLogger(__name__)


class ConversationService:
    """Service for conversation operations"""
    
    def __init__(self, database: AsyncIOMotorDatabase):
        self.db = database
        self.collection = database[Collections.CONVERSATIONS]
    
    async def process_conversation(
        self, 
        unique_id: str, 
        messages: List[ConversationMessage],
        format_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process conversation data and create transcript
        Replaces BasicFeeder + TextAggregator + TransformToJSON from blueprint
        """
        try:
            # Format options with defaults
            options = format_options or {}
            row_separator = options.get("row_separator", "\n")
            include_timestamps = options.get("include_timestamps", True)
            timestamp_format = options.get("timestamp_format", "YYYY-MM-DD HH:mm:ss")
            
            # Process messages (replaces BasicFeeder iteration)
            formatted_lines = []
            for message in messages:
                if include_timestamps:
                    # Format timestamp (replaces formatDate function)
                    timestamp_str = message.timestamp.strftime("%Y-%m-%d %H:%M:%S")
                    line = f"{timestamp_str} - {message.speaker}: {message.text}"
                else:
                    line = f"{message.speaker}: {message.text}"
                
                formatted_lines.append(line)
            
            # Aggregate text (replaces TextAggregator)
            formatted_transcript = row_separator.join(formatted_lines)
            
            # Transform to JSON (replaces TransformToJSON)
            full_transcript = json.dumps(formatted_transcript, ensure_ascii=False)
            
            # Create or update conversation
            conversation_data = {
                "unique_id": unique_id,
                "messages": [msg.dict() for msg in messages],
                "formatted_transcript": formatted_transcript,
                "full_transcript": full_transcript,
                "updated_at": datetime.utcnow()
            }
            
            # Check if conversation exists
            existing = await self.get_conversation_by_unique_id(unique_id)
            
            if existing:
                # Update existing
                await self.collection.update_one(
                    {"unique_id": unique_id},
                    {"$set": conversation_data}
                )
                conversation_id = existing.id
            else:
                # Create new
                conversation_data["created_at"] = datetime.utcnow()
                result = await self.collection.insert_one(conversation_data)
                conversation_id = str(result.inserted_id)
            
            logger.info(f"Processed conversation for {unique_id}")
            
            return {
                "conversation_id": conversation_id,
                "unique_id": unique_id,
                "message_count": len(messages),
                "formatted_transcript": formatted_transcript,
                "full_transcript": full_transcript
            }
            
        except Exception as e:
            logger.error(f"Error processing conversation {unique_id}: {e}")
            raise
    
    async def get_conversation_by_unique_id(self, unique_id: str) -> Optional[Conversation]:
        """Get conversation by unique ID"""
        try:
            conversation_data = await self.collection.find_one({"unique_id": unique_id})
            if conversation_data:
                conversation_data["_id"] = str(conversation_data["_id"])
                # Convert message dicts back to ConversationMessage objects
                if "messages" in conversation_data:
                    conversation_data["messages"] = [
                        ConversationMessage(**msg) for msg in conversation_data["messages"]
                    ]
                return Conversation(**conversation_data)
            return None
            
        except Exception as e:
            logger.error(f"Error getting conversation {unique_id}: {e}")
            raise
    
    async def get_conversations(
        self, 
        page: int = 1, 
        size: int = 20, 
        filters: Optional[Dict[str, Any]] = None
    ) -> PaginatedResponse:
        """Get paginated conversations with filtering"""
        try:
            query = {}
            
            if filters:
                if filters.get("search"):
                    search_term = filters["search"]
                    query["$or"] = [
                        {"unique_id": {"$regex": search_term, "$options": "i"}},
                        {"formatted_transcript": {"$regex": search_term, "$options": "i"}}
                    ]
                
                if filters.get("start_date"):
                    start_date = datetime.fromisoformat(filters["start_date"])
                    query["created_at"] = {"$gte": start_date}
                
                if filters.get("end_date"):
                    end_date = datetime.fromisoformat(filters["end_date"])
                    if "created_at" in query:
                        query["created_at"]["$lte"] = end_date
                    else:
                        query["created_at"] = {"$lte": end_date}
            
            # Get total count
            total = await self.collection.count_documents(query)
            
            # Calculate pagination
            skip = (page - 1) * size
            pages = math.ceil(total / size)
            
            # Get paginated results
            cursor = self.collection.find(query).sort("created_at", -1).skip(skip).limit(size)
            
            conversations = []
            async for conversation_data in cursor:
                conversation_data["_id"] = str(conversation_data["_id"])
                # Convert message dicts back to ConversationMessage objects
                if "messages" in conversation_data:
                    conversation_data["messages"] = [
                        ConversationMessage(**msg) for msg in conversation_data["messages"]
                    ]
                conversations.append(Conversation(**conversation_data))
            
            return PaginatedResponse(
                items=conversations,
                total=total,
                page=page,
                size=size,
                pages=pages
            )
            
        except Exception as e:
            logger.error(f"Error getting conversations: {e}")
            raise
    
    async def delete_conversation(self, unique_id: str) -> bool:
        """Delete conversation"""
        try:
            result = await self.collection.delete_one({"unique_id": unique_id})
            logger.info(f"Deleted conversation {unique_id}")
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error(f"Error deleting conversation {unique_id}: {e}")
            raise
    
    async def reprocess_conversation(
        self, 
        unique_id: str, 
        format_options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Reprocess existing conversation with new format options"""
        try:
            conversation = await self.get_conversation_by_unique_id(unique_id)
            if not conversation:
                raise ValueError(f"Conversation {unique_id} not found")
            
            # Reprocess with new options
            return await self.process_conversation(
                unique_id=unique_id,
                messages=conversation.messages,
                format_options=format_options
            )
            
        except Exception as e:
            logger.error(f"Error reprocessing conversation {unique_id}: {e}")
            raise
    
    async def get_conversation_analytics(self, unique_id: str) -> Optional[Dict[str, Any]]:
        """Get conversation analytics and insights"""
        try:
            conversation = await self.get_conversation_by_unique_id(unique_id)
            if not conversation:
                return None
            
            analytics = {
                "unique_id": unique_id,
                "message_count": len(conversation.messages),
                "total_characters": len(conversation.formatted_transcript or ""),
                "speakers": [],
                "speaker_stats": {},
                "duration_analysis": {}
            }
            
            # Analyze speakers
            speakers = set()
            speaker_message_counts = {}
            speaker_character_counts = {}
            
            for message in conversation.messages:
                speakers.add(message.speaker)
                speaker_message_counts[message.speaker] = speaker_message_counts.get(message.speaker, 0) + 1
                speaker_character_counts[message.speaker] = speaker_character_counts.get(message.speaker, 0) + len(message.text)
            
            analytics["speakers"] = list(speakers)
            analytics["speaker_stats"] = {
                speaker: {
                    "message_count": speaker_message_counts[speaker],
                    "character_count": speaker_character_counts[speaker],
                    "avg_message_length": speaker_character_counts[speaker] / speaker_message_counts[speaker]
                }
                for speaker in speakers
            }
            
            # Duration analysis
            if len(conversation.messages) > 1:
                first_message = conversation.messages[0]
                last_message = conversation.messages[-1]
                duration = (last_message.timestamp - first_message.timestamp).total_seconds()
                
                analytics["duration_analysis"] = {
                    "total_duration_seconds": duration,
                    "total_duration_minutes": round(duration / 60, 2),
                    "messages_per_minute": round(len(conversation.messages) / (duration / 60), 2) if duration > 0 else 0
                }
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error getting conversation analytics {unique_id}: {e}")
            raise
    
    async def search_conversations_by_text(
        self, 
        query: str, 
        page: int = 1, 
        size: int = 20
    ) -> PaginatedResponse:
        """Search conversations by text content"""
        try:
            search_query = {
                "$text": {"$search": query}
            }
            
            # Get total count
            total = await self.collection.count_documents(search_query)
            
            # Calculate pagination
            skip = (page - 1) * size
            pages = math.ceil(total / size)
            
            # Get paginated results with text score
            cursor = self.collection.find(
                search_query,
                {"score": {"$meta": "textScore"}}
            ).sort([("score", {"$meta": "textScore"})]).skip(skip).limit(size)
            
            conversations = []
            async for conversation_data in cursor:
                conversation_data["_id"] = str(conversation_data["_id"])
                # Convert message dicts back to ConversationMessage objects
                if "messages" in conversation_data:
                    conversation_data["messages"] = [
                        ConversationMessage(**msg) for msg in conversation_data["messages"]
                    ]
                conversations.append(Conversation(**conversation_data))
            
            return PaginatedResponse(
                items=conversations,
                total=total,
                page=page,
                size=size,
                pages=pages
            )
            
        except Exception as e:
            logger.error(f"Error searching conversations: {e}")
            raise
