"""
Form response service for managing form response operations
"""

from motor.motor_asyncio import AsyncIOMotorDatabase
from datetime import datetime
from typing import Optional, Dict, Any, List
import logging
import math

from app.models import FormResponse, FormResponseCreate, FormResponseUpdate, PaginatedResponse
from app.config import Collections

logger = logging.getLogger(__name__)


class FormResponseService:
    """Service for form response operations"""
    
    def __init__(self, database: AsyncIOMotorDatabase):
        self.db = database
        self.collection = database[Collections.FORM_RESPONSES]
    
    async def create_form_response(self, form_response_data: FormResponseCreate) -> FormResponse:
        """Create a new form response"""
        try:
            form_response_dict = form_response_data.dict()
            form_response_dict["created_at"] = datetime.utcnow()
            form_response_dict["updated_at"] = datetime.utcnow()
            
            result = await self.collection.insert_one(form_response_dict)
            form_response_dict["_id"] = str(result.inserted_id)
            
            logger.info(f"Created form response for {form_response_data.unique_id}")
            return FormResponse(**form_response_dict)
            
        except Exception as e:
            logger.error(f"Error creating form response: {e}")
            raise
    
    async def get_form_response_by_unique_id(self, unique_id: str) -> Optional[FormResponse]:
        """Get form response by unique ID"""
        try:
            form_response_data = await self.collection.find_one({"unique_id": unique_id})
            if form_response_data:
                form_response_data["_id"] = str(form_response_data["_id"])
                return FormResponse(**form_response_data)
            return None
            
        except Exception as e:
            logger.error(f"Error getting form response {unique_id}: {e}")
            raise
    
    async def update_form_response(
        self, 
        unique_id: str, 
        update_data: FormResponseUpdate
    ) -> Optional[FormResponse]:
        """Update form response"""
        try:
            update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
            
            if update_dict:
                update_dict["updated_at"] = datetime.utcnow()
                
                result = await self.collection.update_one(
                    {"unique_id": unique_id},
                    {"$set": update_dict}
                )
                
                if result.modified_count > 0:
                    return await self.get_form_response_by_unique_id(unique_id)
            
            return None
            
        except Exception as e:
            logger.error(f"Error updating form response {unique_id}: {e}")
            raise
    
    async def delete_form_response(self, unique_id: str) -> bool:
        """Delete form response"""
        try:
            result = await self.collection.delete_one({"unique_id": unique_id})
            logger.info(f"Deleted form response {unique_id}")
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error(f"Error deleting form response {unique_id}: {e}")
            raise
    
    async def get_form_responses(
        self, 
        page: int = 1, 
        size: int = 20, 
        filters: Optional[Dict[str, Any]] = None
    ) -> PaginatedResponse:
        """Get paginated form responses with filtering"""
        try:
            query = {}
            
            if filters:
                if filters.get("form_type"):
                    query["form_type"] = filters["form_type"]
                
                if filters.get("status"):
                    query["status"] = filters["status"]
                
                if filters.get("start_date"):
                    start_date = datetime.fromisoformat(filters["start_date"])
                    query["created_at"] = {"$gte": start_date}
                
                if filters.get("end_date"):
                    end_date = datetime.fromisoformat(filters["end_date"])
                    if "created_at" in query:
                        query["created_at"]["$lte"] = end_date
                    else:
                        query["created_at"] = {"$lte": end_date}
                
                if filters.get("search"):
                    search_term = filters["search"]
                    query["$or"] = [
                        {"unique_id": {"$regex": search_term, "$options": "i"}},
                        {"form_type": {"$regex": search_term, "$options": "i"}}
                    ]
            
            # Get total count
            total = await self.collection.count_documents(query)
            
            # Calculate pagination
            skip = (page - 1) * size
            pages = math.ceil(total / size)
            
            # Get paginated results
            cursor = self.collection.find(query).sort("created_at", -1).skip(skip).limit(size)
            
            form_responses = []
            async for form_response_data in cursor:
                form_response_data["_id"] = str(form_response_data["_id"])
                form_responses.append(FormResponse(**form_response_data))
            
            return PaginatedResponse(
                items=form_responses,
                total=total,
                page=page,
                size=size,
                pages=pages
            )
            
        except Exception as e:
            logger.error(f"Error getting form responses: {e}")
            raise
    
    async def update_form_response_status(self, unique_id: str, status: str) -> Optional[FormResponse]:
        """Update form response status"""
        try:
            update_data = FormResponseUpdate(status=status)
            return await self.update_form_response(unique_id, update_data)
            
        except Exception as e:
            logger.error(f"Error updating form response status {unique_id}: {e}")
            raise
    
    async def get_form_types(self) -> List[str]:
        """Get list of available form types"""
        try:
            form_types = await self.collection.distinct("form_type")
            return [ft for ft in form_types if ft is not None]
            
        except Exception as e:
            logger.error(f"Error getting form types: {e}")
            raise
    
    async def get_form_responses_stats(self, filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Get form responses statistics"""
        try:
            match_filter = {}
            
            if filters:
                if filters.get("form_type"):
                    match_filter["form_type"] = filters["form_type"]
                
                if filters.get("start_date"):
                    start_date = datetime.fromisoformat(filters["start_date"])
                    match_filter["created_at"] = {"$gte": start_date}
                
                if filters.get("end_date"):
                    end_date = datetime.fromisoformat(filters["end_date"])
                    if "created_at" in match_filter:
                        match_filter["created_at"]["$lte"] = end_date
                    else:
                        match_filter["created_at"] = {"$lte": end_date}
            
            # Get overall stats
            pipeline = [
                {"$match": match_filter},
                {
                    "$group": {
                        "_id": None,
                        "total_responses": {"$sum": 1},
                        "form_types": {"$addToSet": "$form_type"},
                        "statuses": {"$addToSet": "$status"}
                    }
                }
            ]
            
            cursor = self.collection.aggregate(pipeline)
            overall_stats = await cursor.to_list(length=1)
            
            # Get stats by form type
            type_pipeline = [
                {"$match": match_filter},
                {
                    "$group": {
                        "_id": "$form_type",
                        "count": {"$sum": 1}
                    }
                }
            ]
            
            type_cursor = self.collection.aggregate(type_pipeline)
            type_stats = {}
            async for stat in type_cursor:
                type_stats[stat["_id"] or "unknown"] = stat["count"]
            
            # Get stats by status
            status_pipeline = [
                {"$match": match_filter},
                {
                    "$group": {
                        "_id": "$status",
                        "count": {"$sum": 1}
                    }
                }
            ]
            
            status_cursor = self.collection.aggregate(status_pipeline)
            status_stats = {}
            async for stat in status_cursor:
                status_stats[stat["_id"] or "unknown"] = stat["count"]
            
            result = {
                "total_responses": overall_stats[0]["total_responses"] if overall_stats else 0,
                "form_types": overall_stats[0]["form_types"] if overall_stats else [],
                "statuses": overall_stats[0]["statuses"] if overall_stats else [],
                "by_form_type": type_stats,
                "by_status": status_stats
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting form responses stats: {e}")
            raise
    
    async def validate_form_response(self, unique_id: str) -> Optional[Dict[str, Any]]:
        """Validate form response data"""
        try:
            form_response = await self.get_form_response_by_unique_id(unique_id)
            if not form_response:
                return None
            
            validation_result = {
                "unique_id": unique_id,
                "is_valid": True,
                "errors": [],
                "warnings": []
            }
            
            # Basic validation checks
            if not form_response.response_data:
                validation_result["errors"].append("No response data found")
                validation_result["is_valid"] = False
            
            if not form_response.form_type:
                validation_result["warnings"].append("Form type not specified")
            
            # Check for required fields based on form type
            if form_response.form_type:
                required_fields = self._get_required_fields_for_form_type(form_response.form_type)
                for field in required_fields:
                    if field not in form_response.response_data:
                        validation_result["errors"].append(f"Required field '{field}' is missing")
                        validation_result["is_valid"] = False
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error validating form response {unique_id}: {e}")
            raise
    
    def _get_required_fields_for_form_type(self, form_type: str) -> List[str]:
        """Get required fields for a specific form type"""
        # This would typically come from a configuration or database
        # For now, return some common required fields
        common_required = ["name", "email"]
        
        form_type_requirements = {
            "contact": ["name", "email", "phone"],
            "feedback": ["rating", "comments"],
            "survey": ["responses"]
        }
        
        return form_type_requirements.get(form_type, common_required)
