"""
Call log service for managing call log operations
"""

from motor.motor_asyncio import AsyncIOMotorDatabase
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
import logging
import math

from app.models import Call<PERSON>og, CallLogCreate, CallLogUpdate, PaginatedResponse
from app.config import Collections

logger = logging.getLogger(__name__)


class CallLogService:
    """Service for call log operations"""
    
    def __init__(self, database: AsyncIOMotorDatabase):
        self.db = database
        self.collection = database[Collections.CALL_LOGS]
    
    async def create_call_log(self, call_log_data: CallLogCreate) -> CallLog:
        """Create a new call log"""
        try:
            call_log_dict = call_log_data.dict()
            call_log_dict["created_at"] = datetime.utcnow()
            call_log_dict["updated_at"] = datetime.utcnow()
            
            # Calculate duration in minutes if not provided
            if (call_log_dict.get("call_start_time") and 
                call_log_dict.get("call_end_time") and 
                not call_log_dict.get("call_duration_min")):
                
                duration_sec = (call_log_dict["call_end_time"] - 
                               call_log_dict["call_start_time"]).total_seconds()
                call_log_dict["call_duration_sec"] = int(duration_sec)
                call_log_dict["call_duration_min"] = round(duration_sec / 60, 2)
            
            result = await self.collection.insert_one(call_log_dict)
            call_log_dict["_id"] = str(result.inserted_id)
            
            logger.info(f"Created call log for {call_log_data.unique_id}")
            return CallLog(**call_log_dict)
            
        except Exception as e:
            logger.error(f"Error creating call log: {e}")
            raise
    
    async def get_call_log_by_unique_id(self, unique_id: str) -> Optional[CallLog]:
        """Get call log by unique ID"""
        try:
            call_log_data = await self.collection.find_one({"unique_id": unique_id})
            if call_log_data:
                call_log_data["_id"] = str(call_log_data["_id"])
                return CallLog(**call_log_data)
            return None
            
        except Exception as e:
            logger.error(f"Error getting call log {unique_id}: {e}")
            raise
    
    async def update_call_log(self, unique_id: str, update_data: CallLogUpdate) -> Optional[CallLog]:
        """Update call log"""
        try:
            update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
            
            if update_dict:
                update_dict["updated_at"] = datetime.utcnow()
                
                # Recalculate duration if times are updated
                if "call_start_time" in update_dict or "call_end_time" in update_dict:
                    existing = await self.get_call_log_by_unique_id(unique_id)
                    if existing:
                        start_time = update_dict.get("call_start_time", existing.call_start_time)
                        end_time = update_dict.get("call_end_time", existing.call_end_time)
                        
                        if start_time and end_time:
                            duration_sec = (end_time - start_time).total_seconds()
                            update_dict["call_duration_sec"] = int(duration_sec)
                            update_dict["call_duration_min"] = round(duration_sec / 60, 2)
                
                result = await self.collection.update_one(
                    {"unique_id": unique_id},
                    {"$set": update_dict}
                )
                
                if result.modified_count > 0:
                    return await self.get_call_log_by_unique_id(unique_id)
            
            return None
            
        except Exception as e:
            logger.error(f"Error updating call log {unique_id}: {e}")
            raise
    
    async def delete_call_log(self, unique_id: str) -> bool:
        """Delete call log"""
        try:
            result = await self.collection.delete_one({"unique_id": unique_id})
            logger.info(f"Deleted call log {unique_id}")
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error(f"Error deleting call log {unique_id}: {e}")
            raise
    
    async def get_call_logs(
        self, 
        page: int = 1, 
        size: int = 20, 
        filters: Optional[Dict[str, Any]] = None
    ) -> PaginatedResponse:
        """Get paginated call logs with filtering"""
        try:
            query = {}
            
            if filters:
                if filters.get("status"):
                    query["status"] = filters["status"]
                
                if filters.get("start_date"):
                    start_date = datetime.fromisoformat(filters["start_date"])
                    query["call_start_time"] = {"$gte": start_date}
                
                if filters.get("end_date"):
                    end_date = datetime.fromisoformat(filters["end_date"])
                    if "call_start_time" in query:
                        query["call_start_time"]["$lte"] = end_date
                    else:
                        query["call_start_time"] = {"$lte": end_date}
                
                if filters.get("search"):
                    search_term = filters["search"]
                    query["$or"] = [
                        {"unique_id": {"$regex": search_term, "$options": "i"}},
                        {"notes": {"$regex": search_term, "$options": "i"}}
                    ]
            
            # Get total count
            total = await self.collection.count_documents(query)
            
            # Calculate pagination
            skip = (page - 1) * size
            pages = math.ceil(total / size)
            
            # Get paginated results
            cursor = self.collection.find(query).sort("created_at", -1).skip(skip).limit(size)
            
            call_logs = []
            async for call_log_data in cursor:
                call_log_data["_id"] = str(call_log_data["_id"])
                call_logs.append(CallLog(**call_log_data))
            
            return PaginatedResponse(
                items=call_logs,
                total=total,
                page=page,
                size=size,
                pages=pages
            )
            
        except Exception as e:
            logger.error(f"Error getting call logs: {e}")
            raise
    
    async def upsert_call_log(self, unique_id: str, call_data: Dict[str, Any]) -> CallLog:
        """Update existing call log or create new one"""
        try:
            existing = await self.get_call_log_by_unique_id(unique_id)
            
            if existing:
                # Update existing
                update_data = CallLogUpdate(**call_data)
                updated = await self.update_call_log(unique_id, update_data)
                return updated or existing
            else:
                # Create new
                create_data = CallLogCreate(unique_id=unique_id, **call_data)
                return await self.create_call_log(create_data)
                
        except Exception as e:
            logger.error(f"Error upserting call log {unique_id}: {e}")
            raise
    
    async def update_call_log_status(self, unique_id: str, status: str) -> Optional[CallLog]:
        """Update call log status"""
        try:
            update_data = CallLogUpdate(status=status)
            return await self.update_call_log(unique_id, update_data)
            
        except Exception as e:
            logger.error(f"Error updating call log status {unique_id}: {e}")
            raise
    
    async def get_call_log_summary(self, unique_id: str) -> Optional[Dict[str, Any]]:
        """Get call log summary with computed fields"""
        try:
            call_log = await self.get_call_log_by_unique_id(unique_id)
            if not call_log:
                return None
            
            summary = call_log.dict()
            
            # Add computed fields
            if call_log.call_duration_min:
                summary["duration_formatted"] = f"{call_log.call_duration_min:.1f} minutes"
            
            if call_log.call_start_time:
                summary["call_date"] = call_log.call_start_time.strftime("%Y-%m-%d")
                summary["call_time"] = call_log.call_start_time.strftime("%H:%M:%S")
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting call log summary {unique_id}: {e}")
            raise
    
    async def get_call_logs_stats(self, filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Get call logs statistics"""
        try:
            match_filter = {}
            
            if filters:
                if filters.get("start_date"):
                    start_date = datetime.fromisoformat(filters["start_date"])
                    match_filter["call_start_time"] = {"$gte": start_date}
                
                if filters.get("end_date"):
                    end_date = datetime.fromisoformat(filters["end_date"])
                    if "call_start_time" in match_filter:
                        match_filter["call_start_time"]["$lte"] = end_date
                    else:
                        match_filter["call_start_time"] = {"$lte": end_date}
            
            pipeline = [
                {"$match": match_filter},
                {
                    "$group": {
                        "_id": None,
                        "total_calls": {"$sum": 1},
                        "avg_duration": {"$avg": "$call_duration_min"},
                        "total_duration": {"$sum": "$call_duration_min"},
                        "max_duration": {"$max": "$call_duration_min"},
                        "min_duration": {"$min": "$call_duration_min"}
                    }
                }
            ]
            
            cursor = self.collection.aggregate(pipeline)
            stats = await cursor.to_list(length=1)
            
            if stats:
                result = stats[0]
                result.pop("_id", None)
                return result
            
            return {
                "total_calls": 0,
                "avg_duration": 0,
                "total_duration": 0,
                "max_duration": 0,
                "min_duration": 0
            }
            
        except Exception as e:
            logger.error(f"Error getting call logs stats: {e}")
            raise
