"""
Webhook service for managing webhook operations
"""

from motor.motor_asyncio import AsyncIOMotorDatabase
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import logging

from app.models import WebhookLog, WebhookStatus
from app.config import Collections

logger = logging.getLogger(__name__)


class WebhookService:
    """Service for webhook operations"""
    
    def __init__(self, database: AsyncIOMotorDatabase):
        self.db = database
        self.collection = database[Collections.WEBHOOK_LOGS]
    
    async def create_webhook_log(
        self,
        webhook_id: str,
        unique_id: str,
        payload: Dict[str, Any],
        status: WebhookStatus = WebhookStatus.PENDING
    ) -> WebhookLog:
        """Create a new webhook log entry"""
        try:
            webhook_data = {
                "webhook_id": webhook_id,
                "unique_id": unique_id,
                "status": status.value,
                "payload": payload,
                "error_message": None,
                "retry_count": 0,
                "processed_at": None,
                "created_at": datetime.utcnow()
            }
            
            result = await self.collection.insert_one(webhook_data)
            webhook_data["_id"] = str(result.inserted_id)
            
            logger.info(f"Created webhook log for {webhook_id}")
            return WebhookLog(**webhook_data)
            
        except Exception as e:
            logger.error(f"Error creating webhook log: {e}")
            raise
    
    async def get_webhook_log(self, webhook_id: str) -> Optional[WebhookLog]:
        """Get webhook log by webhook ID"""
        try:
            webhook_data = await self.collection.find_one({"webhook_id": webhook_id})
            if webhook_data:
                webhook_data["_id"] = str(webhook_data["_id"])
                return WebhookLog(**webhook_data)
            return None
            
        except Exception as e:
            logger.error(f"Error getting webhook log {webhook_id}: {e}")
            raise
    
    async def update_webhook_log(
        self,
        webhook_id: str,
        status: Optional[WebhookStatus] = None,
        error_message: Optional[str] = None,
        retry_count: Optional[int] = None,
        processed_at: Optional[datetime] = None
    ) -> bool:
        """Update webhook log"""
        try:
            update_data = {}
            
            if status:
                update_data["status"] = status.value
            
            if error_message is not None:
                update_data["error_message"] = error_message
            
            if retry_count is not None:
                update_data["retry_count"] = retry_count
            
            if processed_at:
                update_data["processed_at"] = processed_at
            
            if update_data:
                result = await self.collection.update_one(
                    {"webhook_id": webhook_id},
                    {"$set": update_data}
                )
                
                logger.info(f"Updated webhook log {webhook_id}")
                return result.modified_count > 0
            
            return False
            
        except Exception as e:
            logger.error(f"Error updating webhook log {webhook_id}: {e}")
            raise
    
    async def get_webhook_logs_by_unique_id(self, unique_id: str) -> list:
        """Get all webhook logs for a unique ID"""
        try:
            cursor = self.collection.find({"unique_id": unique_id}).sort("created_at", -1)
            webhook_logs = []
            
            async for webhook_data in cursor:
                webhook_data["_id"] = str(webhook_data["_id"])
                webhook_logs.append(WebhookLog(**webhook_data))
            
            return webhook_logs
            
        except Exception as e:
            logger.error(f"Error getting webhook logs for {unique_id}: {e}")
            raise
    
    async def get_failed_webhooks(self, limit: int = 100) -> list:
        """Get failed webhooks for retry processing"""
        try:
            cursor = self.collection.find({
                "status": {"$in": [WebhookStatus.FAILED.value, WebhookStatus.RETRY.value]}
            }).sort("created_at", 1).limit(limit)
            
            webhook_logs = []
            async for webhook_data in cursor:
                webhook_data["_id"] = str(webhook_data["_id"])
                webhook_logs.append(WebhookLog(**webhook_data))
            
            return webhook_logs
            
        except Exception as e:
            logger.error(f"Error getting failed webhooks: {e}")
            raise
    
    async def cleanup_old_webhook_logs(self, days: int = 30) -> int:
        """Clean up old webhook logs"""
        try:
            cutoff_date = datetime.utcnow().replace(
                hour=0, minute=0, second=0, microsecond=0
            ) - timedelta(days=days)
            
            result = await self.collection.delete_many({
                "created_at": {"$lt": cutoff_date},
                "status": {"$in": [WebhookStatus.SUCCESS.value, WebhookStatus.FAILED.value]}
            })
            
            logger.info(f"Cleaned up {result.deleted_count} old webhook logs")
            return result.deleted_count
            
        except Exception as e:
            logger.error(f"Error cleaning up webhook logs: {e}")
            raise
    
    async def get_webhook_stats(self, start_date: Optional[datetime] = None) -> Dict[str, Any]:
        """Get webhook processing statistics"""
        try:
            match_filter = {}
            if start_date:
                match_filter["created_at"] = {"$gte": start_date}
            
            pipeline = [
                {"$match": match_filter},
                {
                    "$group": {
                        "_id": "$status",
                        "count": {"$sum": 1},
                        "avg_retry_count": {"$avg": "$retry_count"}
                    }
                }
            ]
            
            cursor = self.collection.aggregate(pipeline)
            stats = {}
            total_count = 0
            
            async for stat in cursor:
                status = stat["_id"]
                count = stat["count"]
                stats[status] = {
                    "count": count,
                    "avg_retry_count": stat.get("avg_retry_count", 0)
                }
                total_count += count
            
            stats["total"] = total_count
            return stats
            
        except Exception as e:
            logger.error(f"Error getting webhook stats: {e}")
            raise
