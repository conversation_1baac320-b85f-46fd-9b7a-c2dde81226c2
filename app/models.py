"""
Pydantic models for the application
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


class WebhookStatus(str, Enum):
    """Webhook processing status"""
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    RETRY = "retry"


class ConversationMessage(BaseModel):
    """Individual conversation message"""
    timestamp: datetime
    speaker: str
    text: str


class WebhookPayload(BaseModel):
    """Incoming webhook payload"""
    unique_id: str = Field(..., alias="Unique_id")
    conversation: List[ConversationMessage] = []
    call_start_time: Optional[datetime] = None
    call_end_time: Optional[datetime] = None
    call_duration: Optional[int] = None
    additional_data: Optional[Dict[str, Any]] = {}
    
    class Config:
        allow_population_by_field_name = True


class CallLogBase(BaseModel):
    """Base call log model"""
    unique_id: str
    call_start_time: Optional[datetime] = None
    call_end_time: Optional[datetime] = None
    call_duration_sec: Optional[int] = None
    call_duration_min: Optional[float] = None
    status: Optional[str] = None
    notes: Optional[str] = None


class CallLogCreate(CallLogBase):
    """Call log creation model"""
    pass


class CallLogUpdate(BaseModel):
    """Call log update model"""
    call_start_time: Optional[datetime] = None
    call_end_time: Optional[datetime] = None
    call_duration_sec: Optional[int] = None
    call_duration_min: Optional[float] = None
    status: Optional[str] = None
    notes: Optional[str] = None
    updated_at: Optional[datetime] = None


class CallLog(CallLogBase):
    """Call log response model"""
    id: str = Field(..., alias="_id")
    created_at: datetime
    updated_at: datetime
    
    class Config:
        allow_population_by_field_name = True


class ConversationBase(BaseModel):
    """Base conversation model"""
    unique_id: str
    messages: List[ConversationMessage] = []
    full_transcript: Optional[str] = None
    formatted_transcript: Optional[str] = None


class ConversationCreate(ConversationBase):
    """Conversation creation model"""
    pass


class Conversation(ConversationBase):
    """Conversation response model"""
    id: str = Field(..., alias="_id")
    created_at: datetime
    updated_at: datetime
    
    class Config:
        allow_population_by_field_name = True


class FormResponseBase(BaseModel):
    """Base form response model"""
    unique_id: str
    response_data: Dict[str, Any] = {}
    form_type: Optional[str] = None
    status: Optional[str] = None


class FormResponseCreate(FormResponseBase):
    """Form response creation model"""
    pass


class FormResponseUpdate(BaseModel):
    """Form response update model"""
    response_data: Optional[Dict[str, Any]] = None
    form_type: Optional[str] = None
    status: Optional[str] = None
    updated_at: Optional[datetime] = None


class FormResponse(FormResponseBase):
    """Form response model"""
    id: str = Field(..., alias="_id")
    created_at: datetime
    updated_at: datetime
    
    class Config:
        allow_population_by_field_name = True


class WebhookLog(BaseModel):
    """Webhook log model"""
    id: str = Field(..., alias="_id")
    webhook_id: str
    unique_id: str
    status: WebhookStatus
    payload: Dict[str, Any]
    error_message: Optional[str] = None
    retry_count: int = 0
    processed_at: Optional[datetime] = None
    created_at: datetime
    
    class Config:
        allow_population_by_field_name = True


class PaginationParams(BaseModel):
    """Pagination parameters"""
    page: int = Field(1, ge=1)
    size: int = Field(20, ge=1, le=100)


class PaginatedResponse(BaseModel):
    """Paginated response model"""
    items: List[Any]
    total: int
    page: int
    size: int
    pages: int


class ProcessConversationRequest(BaseModel):
    """Request model for processing conversations"""
    unique_id: str
    conversation: List[ConversationMessage]
    format_options: Optional[Dict[str, Any]] = {}


class ExportRequest(BaseModel):
    """Export request model"""
    format: str = Field("json", regex="^(json|csv|xlsx)$")
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    filters: Optional[Dict[str, Any]] = {}


class HealthCheck(BaseModel):
    """Health check response"""
    status: str
    timestamp: datetime
    database_connected: bool
    version: str
