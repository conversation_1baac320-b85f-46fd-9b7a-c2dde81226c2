"""
Form responses endpoints for managing form response data
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional, List
import logging

from app.models import (
    FormResponse, FormResponseCreate, FormResponseUpdate,
    PaginationParams, PaginatedResponse
)
from app.services.form_response_service import FormResponseService
from app.database import get_database

logger = logging.getLogger(__name__)

router = APIRouter()


async def get_form_response_service():
    """Dependency to get form response service"""
    db = await get_database()
    return FormResponseService(db)


@router.get("/form-responses", response_model=PaginatedResponse)
async def get_form_responses(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    form_type: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    form_response_service: FormResponseService = Depends(get_form_response_service)
):
    """
    Get paginated list of form responses with optional filtering
    Replaces Google Sheets filterRows functionality for form responses
    """
    try:
        filters = {}
        
        if form_type:
            filters["form_type"] = form_type
            
        if status:
            filters["status"] = status
        
        if start_date:
            filters["start_date"] = start_date
            
        if end_date:
            filters["end_date"] = end_date
            
        if search:
            filters["search"] = search
        
        result = await form_response_service.get_form_responses(
            page=page,
            size=size,
            filters=filters
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Error getting form responses: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/form-responses/{unique_id}", response_model=FormResponse)
async def get_form_response(
    unique_id: str,
    form_response_service: FormResponseService = Depends(get_form_response_service)
):
    """
    Get form response by unique ID
    Replaces Google Sheets filterRows with specific ID for form responses
    """
    try:
        form_response = await form_response_service.get_form_response_by_unique_id(unique_id)
        if not form_response:
            raise HTTPException(status_code=404, detail="Form response not found")
        
        return form_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting form response {unique_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/form-responses", response_model=FormResponse)
async def create_form_response(
    form_response_data: FormResponseCreate,
    form_response_service: FormResponseService = Depends(get_form_response_service)
):
    """
    Create new form response entry
    Replaces Google Sheets addRow functionality for form responses
    """
    try:
        # Check if form response with unique_id already exists
        existing = await form_response_service.get_form_response_by_unique_id(
            form_response_data.unique_id
        )
        if existing:
            raise HTTPException(
                status_code=400, 
                detail=f"Form response with unique_id {form_response_data.unique_id} already exists"
            )
        
        form_response = await form_response_service.create_form_response(form_response_data)
        return form_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating form response: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/form-responses/{unique_id}", response_model=FormResponse)
async def update_form_response(
    unique_id: str,
    form_response_data: FormResponseUpdate,
    form_response_service: FormResponseService = Depends(get_form_response_service)
):
    """
    Update form response entry
    Replaces Google Sheets updateRow functionality for form responses
    """
    try:
        form_response = await form_response_service.update_form_response(
            unique_id, form_response_data
        )
        if not form_response:
            raise HTTPException(status_code=404, detail="Form response not found")
        
        return form_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating form response {unique_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/form-responses/{unique_id}")
async def delete_form_response(
    unique_id: str,
    form_response_service: FormResponseService = Depends(get_form_response_service)
):
    """Delete form response entry"""
    try:
        success = await form_response_service.delete_form_response(unique_id)
        if not success:
            raise HTTPException(status_code=404, detail="Form response not found")
        
        return {"message": f"Form response {unique_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting form response {unique_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.patch("/form-responses/{unique_id}/status")
async def update_form_response_status(
    unique_id: str,
    status: str,
    form_response_service: FormResponseService = Depends(get_form_response_service)
):
    """Update form response status"""
    try:
        form_response = await form_response_service.update_form_response_status(
            unique_id, status
        )
        if not form_response:
            raise HTTPException(status_code=404, detail="Form response not found")
        
        return {"message": f"Form response {unique_id} status updated to {status}"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating form response status {unique_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/form-responses/types/list")
async def get_form_types(
    form_response_service: FormResponseService = Depends(get_form_response_service)
):
    """Get list of available form types"""
    try:
        form_types = await form_response_service.get_form_types()
        return {"form_types": form_types}
        
    except Exception as e:
        logger.error(f"Error getting form types: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/form-responses/stats/overview")
async def get_form_responses_stats(
    form_type: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    form_response_service: FormResponseService = Depends(get_form_response_service)
):
    """Get form responses statistics and overview"""
    try:
        filters = {}
        if form_type:
            filters["form_type"] = form_type
        if start_date:
            filters["start_date"] = start_date
        if end_date:
            filters["end_date"] = end_date
            
        stats = await form_response_service.get_form_responses_stats(filters)
        return stats
        
    except Exception as e:
        logger.error(f"Error getting form responses stats: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/form-responses/{unique_id}/validation")
async def validate_form_response(
    unique_id: str,
    form_response_service: FormResponseService = Depends(get_form_response_service)
):
    """Validate form response data"""
    try:
        validation_result = await form_response_service.validate_form_response(unique_id)
        if not validation_result:
            raise HTTPException(status_code=404, detail="Form response not found")
        
        return validation_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating form response {unique_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
