"""
Webhook endpoints for receiving and processing webhook data
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, Request
from datetime import datetime
import logging
import uuid
from typing import Dict, Any

from app.models import WebhookPayload, WebhookLog, WebhookStatus
from app.services.webhook_service import WebhookService
from app.services.call_log_service import CallLogService
from app.services.conversation_service import ConversationService
from app.database import get_database

logger = logging.getLogger(__name__)

router = APIRouter()


async def get_webhook_service():
    """Dependency to get webhook service"""
    db = await get_database()
    return WebhookService(db)


async def get_call_log_service():
    """Dependency to get call log service"""
    db = await get_database()
    return CallLogService(db)


async def get_conversation_service():
    """Dependency to get conversation service"""
    db = await get_database()
    return ConversationService(db)


@router.post("/webhook/after-call")
async def receive_after_call_webhook(
    payload: Dict[str, Any],
    background_tasks: BackgroundTasks,
    request: Request,
    webhook_service: WebhookService = Depends(get_webhook_service)
):
    """
    Receive webhook data from call system (After Call Receiver)
    This replaces the gateway:CustomWebHook module from the blueprint
    """
    webhook_id = str(uuid.uuid4())
    
    try:
        # Log incoming webhook
        logger.info(f"Received webhook {webhook_id}: {payload}")
        
        # Validate and parse payload
        webhook_payload = WebhookPayload(**payload)
        
        # Create webhook log entry
        webhook_log = await webhook_service.create_webhook_log(
            webhook_id=webhook_id,
            unique_id=webhook_payload.unique_id,
            payload=payload,
            status=WebhookStatus.PENDING
        )
        
        # Process webhook in background
        background_tasks.add_task(
            process_webhook_data,
            webhook_id,
            webhook_payload,
            webhook_service
        )
        
        return {
            "webhook_id": webhook_id,
            "status": "received",
            "unique_id": webhook_payload.unique_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error processing webhook {webhook_id}: {e}")
        
        # Log failed webhook
        if 'webhook_payload' in locals():
            await webhook_service.update_webhook_log(
                webhook_id=webhook_id,
                status=WebhookStatus.FAILED,
                error_message=str(e)
            )
        
        raise HTTPException(
            status_code=400,
            detail=f"Invalid webhook payload: {str(e)}"
        )


async def process_webhook_data(
    webhook_id: str,
    payload: WebhookPayload,
    webhook_service: WebhookService
):
    """
    Background task to process webhook data
    This replaces the entire flow from the blueprint
    """
    try:
        # Update status to processing
        await webhook_service.update_webhook_log(
            webhook_id=webhook_id,
            status=WebhookStatus.PROCESSING
        )
        
        # Get services
        db = await get_database()
        call_log_service = CallLogService(db)
        conversation_service = ConversationService(db)
        
        # Process conversation data (replaces BasicFeeder + TextAggregator)
        if payload.conversation:
            await conversation_service.process_conversation(
                unique_id=payload.unique_id,
                messages=payload.conversation
            )
        
        # Update or create call log (replaces Google Sheets filterRows + updateRow)
        await call_log_service.upsert_call_log(
            unique_id=payload.unique_id,
            call_data={
                "call_start_time": payload.call_start_time,
                "call_end_time": payload.call_end_time,
                "call_duration": payload.call_duration,
                **payload.additional_data
            }
        )
        
        # Mark webhook as successful
        await webhook_service.update_webhook_log(
            webhook_id=webhook_id,
            status=WebhookStatus.SUCCESS,
            processed_at=datetime.utcnow()
        )
        
        logger.info(f"Successfully processed webhook {webhook_id}")
        
    except Exception as e:
        logger.error(f"Error in background processing for webhook {webhook_id}: {e}")
        
        # Mark webhook as failed
        await webhook_service.update_webhook_log(
            webhook_id=webhook_id,
            status=WebhookStatus.FAILED,
            error_message=str(e)
        )


@router.get("/webhook/status/{webhook_id}")
async def get_webhook_status(
    webhook_id: str,
    webhook_service: WebhookService = Depends(get_webhook_service)
):
    """Get webhook processing status"""
    try:
        webhook_log = await webhook_service.get_webhook_log(webhook_id)
        if not webhook_log:
            raise HTTPException(status_code=404, detail="Webhook not found")
        
        return {
            "webhook_id": webhook_id,
            "status": webhook_log.status,
            "unique_id": webhook_log.unique_id,
            "created_at": webhook_log.created_at,
            "processed_at": webhook_log.processed_at,
            "error_message": webhook_log.error_message,
            "retry_count": webhook_log.retry_count
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting webhook status {webhook_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/webhook/retry/{webhook_id}")
async def retry_webhook(
    webhook_id: str,
    background_tasks: BackgroundTasks,
    webhook_service: WebhookService = Depends(get_webhook_service)
):
    """Retry failed webhook processing"""
    try:
        webhook_log = await webhook_service.get_webhook_log(webhook_id)
        if not webhook_log:
            raise HTTPException(status_code=404, detail="Webhook not found")
        
        if webhook_log.status not in [WebhookStatus.FAILED, WebhookStatus.RETRY]:
            raise HTTPException(
                status_code=400, 
                detail="Webhook is not in a retryable state"
            )
        
        # Parse original payload
        webhook_payload = WebhookPayload(**webhook_log.payload)
        
        # Update retry count and status
        await webhook_service.update_webhook_log(
            webhook_id=webhook_id,
            status=WebhookStatus.RETRY,
            retry_count=webhook_log.retry_count + 1
        )
        
        # Process webhook in background
        background_tasks.add_task(
            process_webhook_data,
            webhook_id,
            webhook_payload,
            webhook_service
        )
        
        return {
            "webhook_id": webhook_id,
            "status": "retry_initiated",
            "retry_count": webhook_log.retry_count + 1
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrying webhook {webhook_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
