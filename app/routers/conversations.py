"""
Conversations endpoints for managing conversation data and transcripts
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional, List
import logging

from app.models import (
    Conversation, ConversationCreate, ProcessConversationRequest,
    PaginationParams, PaginatedResponse
)
from app.services.conversation_service import ConversationService
from app.database import get_database

logger = logging.getLogger(__name__)

router = APIRouter()


async def get_conversation_service():
    """Dependency to get conversation service"""
    db = await get_database()
    return ConversationService(db)


@router.get("/conversations", response_model=PaginatedResponse)
async def get_conversations(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """
    Get paginated list of conversations with optional filtering
    """
    try:
        filters = {}
        
        if search:
            filters["search"] = search
            
        if start_date:
            filters["start_date"] = start_date
            
        if end_date:
            filters["end_date"] = end_date
        
        result = await conversation_service.get_conversations(
            page=page,
            size=size,
            filters=filters
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Error getting conversations: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/conversations/{unique_id}", response_model=Conversation)
async def get_conversation(
    unique_id: str,
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """
    Get conversation by unique ID
    """
    try:
        conversation = await conversation_service.get_conversation_by_unique_id(unique_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        return conversation
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting conversation {unique_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/conversations/process")
async def process_conversation(
    request: ProcessConversationRequest,
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """
    Process conversation data and create transcript
    Replaces BasicFeeder + TextAggregator + TransformToJSON from blueprint
    """
    try:
        result = await conversation_service.process_conversation(
            unique_id=request.unique_id,
            messages=request.conversation,
            format_options=request.format_options
        )
        
        return {
            "unique_id": request.unique_id,
            "status": "processed",
            "message_count": len(request.conversation),
            "transcript_length": len(result.get("formatted_transcript", "")),
            "conversation_id": result.get("conversation_id")
        }
        
    except Exception as e:
        logger.error(f"Error processing conversation {request.unique_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/conversations/{unique_id}/transcript")
async def get_conversation_transcript(
    unique_id: str,
    format: str = Query("formatted", regex="^(formatted|raw|json)$"),
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """
    Get conversation transcript in different formats
    """
    try:
        conversation = await conversation_service.get_conversation_by_unique_id(unique_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        if format == "formatted":
            return {
                "unique_id": unique_id,
                "transcript": conversation.formatted_transcript,
                "format": "formatted"
            }
        elif format == "raw":
            return {
                "unique_id": unique_id,
                "messages": conversation.messages,
                "format": "raw"
            }
        elif format == "json":
            return {
                "unique_id": unique_id,
                "transcript": conversation.full_transcript,
                "format": "json"
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting conversation transcript {unique_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/conversations/{unique_id}/reprocess")
async def reprocess_conversation(
    unique_id: str,
    format_options: Optional[dict] = None,
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """
    Reprocess existing conversation with new format options
    """
    try:
        conversation = await conversation_service.get_conversation_by_unique_id(unique_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        result = await conversation_service.reprocess_conversation(
            unique_id=unique_id,
            format_options=format_options or {}
        )
        
        return {
            "unique_id": unique_id,
            "status": "reprocessed",
            "message": "Conversation reprocessed successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reprocessing conversation {unique_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/conversations/{unique_id}")
async def delete_conversation(
    unique_id: str,
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """Delete conversation"""
    try:
        success = await conversation_service.delete_conversation(unique_id)
        if not success:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        return {"message": f"Conversation {unique_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting conversation {unique_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/conversations/{unique_id}/analytics")
async def get_conversation_analytics(
    unique_id: str,
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """Get conversation analytics and insights"""
    try:
        analytics = await conversation_service.get_conversation_analytics(unique_id)
        if not analytics:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        return analytics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting conversation analytics {unique_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/conversations/search/text")
async def search_conversations_by_text(
    query: str = Query(..., min_length=3),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """Search conversations by text content"""
    try:
        result = await conversation_service.search_conversations_by_text(
            query=query,
            page=page,
            size=size
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Error searching conversations: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
