"""
Call logs endpoints for managing call log data
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional, List
import logging

from app.models import (
    CallLog, CallLogCreate, CallLogUpdate, 
    PaginationParams, PaginatedResponse
)
from app.services.call_log_service import CallLogService
from app.database import get_database

logger = logging.getLogger(__name__)

router = APIRouter()


async def get_call_log_service():
    """Dependency to get call log service"""
    db = await get_database()
    return CallLogService(db)


@router.get("/call-logs", response_model=PaginatedResponse)
async def get_call_logs(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    call_log_service: CallLogService = Depends(get_call_log_service)
):
    """
    Get paginated list of call logs with optional filtering
    Replaces Google Sheets filterRows functionality
    """
    try:
        filters = {}
        
        if status:
            filters["status"] = status
        
        if start_date:
            filters["start_date"] = start_date
            
        if end_date:
            filters["end_date"] = end_date
            
        if search:
            filters["search"] = search
        
        result = await call_log_service.get_call_logs(
            page=page,
            size=size,
            filters=filters
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Error getting call logs: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/call-logs/{unique_id}", response_model=CallLog)
async def get_call_log(
    unique_id: str,
    call_log_service: CallLogService = Depends(get_call_log_service)
):
    """
    Get call log by unique ID
    Replaces Google Sheets filterRows with specific ID
    """
    try:
        call_log = await call_log_service.get_call_log_by_unique_id(unique_id)
        if not call_log:
            raise HTTPException(status_code=404, detail="Call log not found")
        
        return call_log
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting call log {unique_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/call-logs", response_model=CallLog)
async def create_call_log(
    call_log_data: CallLogCreate,
    call_log_service: CallLogService = Depends(get_call_log_service)
):
    """
    Create new call log entry
    Replaces Google Sheets addRow functionality
    """
    try:
        # Check if call log with unique_id already exists
        existing = await call_log_service.get_call_log_by_unique_id(call_log_data.unique_id)
        if existing:
            raise HTTPException(
                status_code=400, 
                detail=f"Call log with unique_id {call_log_data.unique_id} already exists"
            )
        
        call_log = await call_log_service.create_call_log(call_log_data)
        return call_log
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating call log: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/call-logs/{unique_id}", response_model=CallLog)
async def update_call_log(
    unique_id: str,
    call_log_data: CallLogUpdate,
    call_log_service: CallLogService = Depends(get_call_log_service)
):
    """
    Update call log entry
    Replaces Google Sheets updateRow functionality
    """
    try:
        call_log = await call_log_service.update_call_log(unique_id, call_log_data)
        if not call_log:
            raise HTTPException(status_code=404, detail="Call log not found")
        
        return call_log
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating call log {unique_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/call-logs/{unique_id}")
async def delete_call_log(
    unique_id: str,
    call_log_service: CallLogService = Depends(get_call_log_service)
):
    """Delete call log entry"""
    try:
        success = await call_log_service.delete_call_log(unique_id)
        if not success:
            raise HTTPException(status_code=404, detail="Call log not found")
        
        return {"message": f"Call log {unique_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting call log {unique_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.patch("/call-logs/{unique_id}/status")
async def update_call_log_status(
    unique_id: str,
    status: str,
    call_log_service: CallLogService = Depends(get_call_log_service)
):
    """Update call log status"""
    try:
        call_log = await call_log_service.update_call_log_status(unique_id, status)
        if not call_log:
            raise HTTPException(status_code=404, detail="Call log not found")
        
        return {"message": f"Call log {unique_id} status updated to {status}"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating call log status {unique_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/call-logs/{unique_id}/summary")
async def get_call_log_summary(
    unique_id: str,
    call_log_service: CallLogService = Depends(get_call_log_service)
):
    """Get call log summary with additional computed fields"""
    try:
        summary = await call_log_service.get_call_log_summary(unique_id)
        if not summary:
            raise HTTPException(status_code=404, detail="Call log not found")
        
        return summary
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting call log summary {unique_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/call-logs/stats/overview")
async def get_call_logs_stats(
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    call_log_service: CallLogService = Depends(get_call_log_service)
):
    """Get call logs statistics and overview"""
    try:
        filters = {}
        if start_date:
            filters["start_date"] = start_date
        if end_date:
            filters["end_date"] = end_date
            
        stats = await call_log_service.get_call_logs_stats(filters)
        return stats
        
    except Exception as e:
        logger.error(f"Error getting call logs stats: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
