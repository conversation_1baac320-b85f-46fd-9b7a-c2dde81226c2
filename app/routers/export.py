"""
Export endpoints for data export functionality
Replaces Google Sheets export functionality
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Response
from fastapi.responses import StreamingResponse
from typing import Optional
import logging
import io
import json
import csv

from app.models import ExportRequest
from app.services.export_service import ExportService
from app.database import get_database

logger = logging.getLogger(__name__)

router = APIRouter()


async def get_export_service():
    """Dependency to get export service"""
    db = await get_database()
    return ExportService(db)


@router.get("/export/call-logs")
async def export_call_logs(
    format: str = Query("json", regex="^(json|csv|xlsx)$"),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    export_service: ExportService = Depends(get_export_service)
):
    """
    Export call logs data
    Replaces Google Sheets export functionality
    """
    try:
        filters = {}
        if start_date:
            filters["start_date"] = start_date
        if end_date:
            filters["end_date"] = end_date
        if status:
            filters["status"] = status
        
        export_data = await export_service.export_call_logs(
            format=format,
            filters=filters
        )
        
        if format == "json":
            return Response(
                content=export_data,
                media_type="application/json",
                headers={"Content-Disposition": "attachment; filename=call_logs.json"}
            )
        elif format == "csv":
            return Response(
                content=export_data,
                media_type="text/csv",
                headers={"Content-Disposition": "attachment; filename=call_logs.csv"}
            )
        elif format == "xlsx":
            return Response(
                content=export_data,
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                headers={"Content-Disposition": "attachment; filename=call_logs.xlsx"}
            )
        
    except Exception as e:
        logger.error(f"Error exporting call logs: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/export/form-responses")
async def export_form_responses(
    format: str = Query("json", regex="^(json|csv|xlsx)$"),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    form_type: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    export_service: ExportService = Depends(get_export_service)
):
    """
    Export form responses data
    Replaces Google Sheets export functionality
    """
    try:
        filters = {}
        if start_date:
            filters["start_date"] = start_date
        if end_date:
            filters["end_date"] = end_date
        if form_type:
            filters["form_type"] = form_type
        if status:
            filters["status"] = status
        
        export_data = await export_service.export_form_responses(
            format=format,
            filters=filters
        )
        
        if format == "json":
            return Response(
                content=export_data,
                media_type="application/json",
                headers={"Content-Disposition": "attachment; filename=form_responses.json"}
            )
        elif format == "csv":
            return Response(
                content=export_data,
                media_type="text/csv",
                headers={"Content-Disposition": "attachment; filename=form_responses.csv"}
            )
        elif format == "xlsx":
            return Response(
                content=export_data,
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                headers={"Content-Disposition": "attachment; filename=form_responses.xlsx"}
            )
        
    except Exception as e:
        logger.error(f"Error exporting form responses: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/export/conversations")
async def export_conversations(
    format: str = Query("json", regex="^(json|csv|xlsx)$"),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    include_transcripts: bool = Query(False),
    export_service: ExportService = Depends(get_export_service)
):
    """Export conversations data"""
    try:
        filters = {}
        if start_date:
            filters["start_date"] = start_date
        if end_date:
            filters["end_date"] = end_date
        
        export_data = await export_service.export_conversations(
            format=format,
            filters=filters,
            include_transcripts=include_transcripts
        )
        
        if format == "json":
            return Response(
                content=export_data,
                media_type="application/json",
                headers={"Content-Disposition": "attachment; filename=conversations.json"}
            )
        elif format == "csv":
            return Response(
                content=export_data,
                media_type="text/csv",
                headers={"Content-Disposition": "attachment; filename=conversations.csv"}
            )
        elif format == "xlsx":
            return Response(
                content=export_data,
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                headers={"Content-Disposition": "attachment; filename=conversations.xlsx"}
            )
        
    except Exception as e:
        logger.error(f"Error exporting conversations: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/export/custom")
async def export_custom_data(
    export_request: ExportRequest,
    export_service: ExportService = Depends(get_export_service)
):
    """Export custom data based on request parameters"""
    try:
        export_data = await export_service.export_custom_data(export_request)
        
        filename = f"custom_export.{export_request.format}"
        
        if export_request.format == "json":
            return Response(
                content=export_data,
                media_type="application/json",
                headers={"Content-Disposition": f"attachment; filename={filename}"}
            )
        elif export_request.format == "csv":
            return Response(
                content=export_data,
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename={filename}"}
            )
        elif export_request.format == "xlsx":
            return Response(
                content=export_data,
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                headers={"Content-Disposition": f"attachment; filename={filename}"}
            )
        
    except Exception as e:
        logger.error(f"Error exporting custom data: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/export/templates")
async def get_export_templates(
    export_service: ExportService = Depends(get_export_service)
):
    """Get available export templates"""
    try:
        templates = await export_service.get_export_templates()
        return {"templates": templates}
        
    except Exception as e:
        logger.error(f"Error getting export templates: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/export/status/{export_id}")
async def get_export_status(
    export_id: str,
    export_service: ExportService = Depends(get_export_service)
):
    """Get export job status (for async exports)"""
    try:
        status = await export_service.get_export_status(export_id)
        if not status:
            raise HTTPException(status_code=404, detail="Export job not found")
        
        return status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting export status {export_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
