"""
MongoDB database connection and utilities
"""

from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import ConnectionFailure
import logging
from typing import Optional

from app.config import settings, Collections

logger = logging.getLogger(__name__)

# Global database connection
client: Optional[AsyncIOMotorClient] = None
database: Optional[AsyncIOMotorDatabase] = None


async def init_db():
    """Initialize database connection"""
    global client, database
    
    try:
        # Create MongoDB client
        client = AsyncIOMotorClient(settings.MONGODB_URL)
        
        # Test connection
        await client.admin.command('ping')
        logger.info("Successfully connected to MongoDB")
        
        # Get database
        database = client[settings.DATABASE_NAME]
        
        # Create indexes
        await create_indexes()
        
    except ConnectionFailure as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        raise
    except Exception as e:
        logger.error(f"Database initialization error: {e}")
        raise


async def close_db():
    """Close database connection"""
    global client
    if client:
        client.close()
        logger.info("Database connection closed")


async def get_database() -> AsyncIOMotorDatabase:
    """Get database instance"""
    if database is None:
        raise RuntimeError("Database not initialized")
    return database


async def create_indexes():
    """Create database indexes for better performance"""
    if database is None:
        return
    
    try:
        # Call logs indexes
        await database[Collections.CALL_LOGS].create_index("unique_id", unique=True)
        await database[Collections.CALL_LOGS].create_index("call_start_time")
        await database[Collections.CALL_LOGS].create_index("created_at")
        
        # Conversations indexes
        await database[Collections.CONVERSATIONS].create_index("unique_id", unique=True)
        await database[Collections.CONVERSATIONS].create_index("created_at")
        
        # Form responses indexes
        await database[Collections.FORM_RESPONSES].create_index("unique_id", unique=True)
        await database[Collections.FORM_RESPONSES].create_index("created_at")
        
        # Webhook logs indexes
        await database[Collections.WEBHOOK_LOGS].create_index("webhook_id")
        await database[Collections.WEBHOOK_LOGS].create_index("created_at")
        await database[Collections.WEBHOOK_LOGS].create_index("status")
        
        logger.info("Database indexes created successfully")
        
    except Exception as e:
        logger.error(f"Error creating indexes: {e}")


class DatabaseManager:
    """Database manager class for common operations"""
    
    def __init__(self):
        self.db = None
    
    async def get_db(self) -> AsyncIOMotorDatabase:
        """Get database instance"""
        if self.db is None:
            self.db = await get_database()
        return self.db
    
    async def get_collection(self, collection_name: str):
        """Get collection by name"""
        db = await self.get_db()
        return db[collection_name]


# Create database manager instance
db_manager = DatabaseManager()
