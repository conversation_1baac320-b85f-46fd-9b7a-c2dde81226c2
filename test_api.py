#!/usr/bin/env python3
"""
Simple script to test the API endpoints
"""

import requests
import json
from datetime import datetime, timezone


def test_api():
    """Test the API endpoints"""
    base_url = "http://localhost:8000"
    
    print("🚀 Testing Integration Webhooks API")
    print("=" * 50)
    
    # Test health endpoint
    print("\n1. Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
        return
    
    # Test webhook endpoint
    print("\n2. Testing webhook endpoint...")
    webhook_payload = {
        "Unique_id": "test-call-12345",
        "conversation": [
            {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "speaker": "Agent",
                "text": "Hello, how can I help you today?"
            },
            {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "speaker": "Customer", 
                "text": "I need help with my account balance"
            },
            {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "speaker": "Agent",
                "text": "I'll be happy to help you with that. Let me check your account."
            }
        ],
        "call_start_time": datetime.now(timezone.utc).isoformat(),
        "call_end_time": datetime.now(timezone.utc).isoformat(),
        "call_duration": 180,
        "additional_data": {
            "agent_id": "agent-001",
            "customer_id": "customer-456",
            "call_type": "support"
        }
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/webhook/after-call", json=webhook_payload)
        print(f"   Status: {response.status_code}")
        webhook_result = response.json()
        print(f"   Response: {webhook_result}")
        webhook_id = webhook_result.get("webhook_id")
    except Exception as e:
        print(f"   Error: {e}")
        return
    
    # Test webhook status
    if webhook_id:
        print("\n3. Testing webhook status...")
        try:
            response = requests.get(f"{base_url}/api/v1/webhook/status/{webhook_id}")
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.json()}")
        except Exception as e:
            print(f"   Error: {e}")
    
    # Wait a moment for background processing
    import time
    print("\n4. Waiting for background processing...")
    time.sleep(2)
    
    # Test call logs endpoint
    print("\n5. Testing call logs endpoint...")
    try:
        response = requests.get(f"{base_url}/api/v1/call-logs")
        print(f"   Status: {response.status_code}")
        call_logs = response.json()
        print(f"   Found {call_logs.get('total', 0)} call logs")
        if call_logs.get('items'):
            print(f"   First call log: {call_logs['items'][0]['unique_id']}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test conversations endpoint
    print("\n6. Testing conversations endpoint...")
    try:
        response = requests.get(f"{base_url}/api/v1/conversations")
        print(f"   Status: {response.status_code}")
        conversations = response.json()
        print(f"   Found {conversations.get('total', 0)} conversations")
        if conversations.get('items'):
            print(f"   First conversation: {conversations['items'][0]['unique_id']}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test specific call log
    print("\n7. Testing specific call log...")
    try:
        response = requests.get(f"{base_url}/api/v1/call-logs/test-call-12345")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            call_log = response.json()
            print(f"   Call log duration: {call_log.get('call_duration_min')} minutes")
        else:
            print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test conversation transcript
    print("\n8. Testing conversation transcript...")
    try:
        response = requests.get(f"{base_url}/api/v1/conversations/test-call-12345/transcript")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            transcript = response.json()
            print(f"   Transcript format: {transcript.get('format')}")
            print(f"   Transcript preview: {transcript.get('transcript', '')[:100]}...")
        else:
            print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test form response creation
    print("\n9. Testing form response creation...")
    form_data = {
        "unique_id": "test-call-12345",
        "form_type": "feedback",
        "response_data": {
            "rating": 5,
            "comments": "Great service!",
            "recommend": True
        },
        "status": "completed"
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/form-responses", json=form_data)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            form_response = response.json()
            print(f"   Created form response: {form_response['unique_id']}")
        else:
            print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test export functionality
    print("\n10. Testing export functionality...")
    try:
        response = requests.get(f"{base_url}/api/v1/export/call-logs?format=json")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   Export size: {len(response.content)} bytes")
        else:
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n" + "=" * 50)
    print("✅ API testing completed!")
    print("\n📚 View full API documentation at: http://localhost:8000/docs")


if __name__ == "__main__":
    test_api()
